---
type: "manual"
---

# API 接口开发规范

基于项目的 API 架构和依赖库使用指南，所有 API 接口开发必须遵循以下规范。

## 核心依赖库

本项目使用以下 HTTP 请求库：
- **@shencom/request**: 基于 axios 的封装，优先使用
- **@shencom/api**: 公司特定的 API 客户端
- **axios**: 底层 HTTP 客户端（通过 @shencom/request 使用）

### @shencom/request 使用示例
```typescript
import { http } from '@shencom/request'

// GET请求
const res = await http.get('/api/users')

// POST请求
const res = await http.post('/api/users', { name: 'John', age: 30 })
```

### @shencom/api 使用示例
```typescript
import { ApiUserCreate, ApiUserList } from '@shencom/api'

// 获取用户列表
const { data } = await ApiUserList({ page: 1, size: 10 })

// 创建用户
const result = await ApiUserCreate({ name: '<PERSON>', age: 30 })
```

## API 架构概述

## API 目录结构

### 全局 API 结构
```
src/api/
├── modules/              # API 模块
│   ├── auth.ts          # 认证相关 API
│   ├── user.ts          # 用户相关 API
│   └── system.ts        # 系统相关 API
├── types/               # API 类型定义
│   ├── common.ts        # 通用类型
│   ├── auth.ts          # 认证类型
│   └── user.ts          # 用户类型
├── axios.ts             # Axios 配置
├── api.ts               # API 实例配置
└── index.ts             # API 统一导出
```

### 模块 API 结构（必须遵循）
```
src/modules/user/api/
├── index.ts             # API 统一导出
├── user.ts              # 用户 API 实现
├── types.ts             # API 类型定义
└── constants.ts         # API 常量定义
```

## API 实现规范

### 使用 @shencom/request
```typescript
// src/modules/user/api/user.ts

import { http } from '@shencom/request'
import type { UserInfo, UserListQuery, CreateUserData } from './types'

/**
 * 获取用户列表
 * @param query - 查询参数
 * @returns Promise<ApiResponse<UserListData>>
 */
export function getUserList(query: UserListQuery) {
  return http.get<UserListData>('/api/users', {
    params: query
  })
}

/**
 * 获取用户详情
 * @param id - 用户ID
 * @returns Promise<ApiResponse<UserInfo>>
 */
export function getUserDetail(id: number) {
  return http.get<UserInfo>(`/api/users/${id}`)
}

/**
 * 创建用户
 * @param data - 用户数据
 * @returns Promise<ApiResponse<UserInfo>>
 */
export function createUser(data: CreateUserData) {
  return http.post<UserInfo>('/api/users', data)
}

/**
 * 更新用户
 * @param id - 用户ID
 * @param data - 更新数据
 * @returns Promise<ApiResponse<UserInfo>>
 */
export function updateUser(id: number, data: Partial<CreateUserData>) {
  return http.put<UserInfo>(`/api/users/${id}`, data)
}

/**
 * 删除用户
 * @param id - 用户ID
 * @returns Promise<ApiResponse<void>>
 */
export function deleteUser(id: number) {
  return http.delete<void>(`/api/users/${id}`)
}
```

### 使用 @shencom/api
```typescript
// 当需要使用公司 API 客户端时

import { ApiUserCreate, ApiUserList, ApiUserDetail } from '@shencom/api'
import type { UserListQuery, CreateUserData } from './types'

/**
 * 获取用户列表 (使用 @shencom/api)
 */
export async function getUserListFromApi(query: UserListQuery) {
  const { data } = await ApiUserList(query)
  return data
}

/**
 * 创建用户 (使用 @shencom/api)
 */
export async function createUserFromApi(userData: CreateUserData) {
  return await ApiUserCreate(userData)
}
```

## API 类型定义规范

### 基础类型定义
```typescript
// src/modules/user/api/types.ts

/** 通用 API 响应格式 */
export interface ApiResponse<T = any> {
  /** 响应码 */
  errcode: string
  /** 响应消息 */
  errmsg: string
  /** 响应数据 */
  data: T
}

/** 分页查询基础参数 */
SC.API.IndexBodyInterface

/** 分页响应数据 */
SC.API.IndexInterface<T>
```

### 具体业务类型
```typescript
// src/modules/user/api/types.ts

import type { UserStatus, UserRole } from '../enums'

/** 用户信息 */
export interface UserInfo {
  /** 用户ID */
  id: number
  /** 用户名 */
  username: string
  /** 邮箱 */
  email: string
  /** 真实姓名 */
  realName?: string
  /** 头像URL */
  avatar?: string
  /** 用户状态 */
  status: UserStatus
  /** 用户角色 */
  role: UserRole
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/** 用户列表查询参数 */
export interface UserListQuery extends BaseQuery {
  /** 用户状态筛选 */
  status?: UserStatus
  /** 角色筛选 */
  role?: UserRole
  /** 创建时间范围开始 */
  startDate?: string
  /** 创建时间范围结束 */
  endDate?: string
}

/** 用户列表响应数据 */
export type UserListData = PageData<UserInfo>

/** 创建用户请求数据 */
export interface CreateUserData {
  /** 用户名 */
  username: string
  /** 邮箱 */
  email: string
  /** 密码 */
  password: string
  /** 真实姓名 */
  realName?: string
  /** 用户角色 */
  role: UserRole
}

/** 更新用户请求数据 */
export type UpdateUserData = Partial<Omit<CreateUserData, 'password'>>
```

## API 常量定义

### API 路径常量
```typescript
// src/modules/user/api/constants.ts

/** 用户模块 API 路径 */
export const USER_API_PATHS = {
  /** 用户列表 */
  index: '/api/users',
  /** 用户详情 */
  show: (id: number) => `/api/users/${id}`,
  /** 创建用户 */
  create: '/api/users',
  /** 更新用户 */
  update: (id: number) => `/api/users/${id}`,
  /** 删除用户 */
  delete: (id: number) => `/api/users/${id}`,
  /** 导出用户 */
  export: '/api/users/export',
} as const
```

### 使用常量的 API 实现
```typescript
// src/modules/user/api/user.ts

import { USER_API_PATHS } from './constants'

export function getUserList(query: UserListQuery) {
  return http.get<UserListData>(USER_API_PATHS.LIST, { params: query })
}

export function getUserDetail(id: number) {
  return http.get<UserInfo>(USER_API_PATHS.DETAIL(id))
}

export function createUser(data: CreateUserData) {
  return http.post<UserInfo>(USER_API_PATHS.CREATE, data)
}
```

## 错误处理规范

### 统一错误处理
```typescript
// src/modules/user/api/user.ts

/**
 * 获取用户列表（带错误处理）
 */
export async function getUserListSafe(query: UserListQuery) {
  try {
    const response = await http.get<UserListData>('/api/users', {
      params: query
    })
  } catch (error) {
    // 提示错误信息
    console.error('获取用户列表失败:', error)
  }
}
```


## API Mock 规范

### Mock 数据结构
```typescript
// src/mock/user.ts

import { faker } from '@faker-js/faker'
import type { UserInfo, UserListData } from '@/modules/user/api/types'

/** 生成模拟用户数据 */
export function generateMockUser(): UserInfo {
  return {
    id: faker.number.int({ min: 1, max: 1000 }),
    username: faker.internet.userName(),
    email: faker.internet.email(),
    realName: faker.person.fullName(),
    avatar: faker.image.avatar(),
    status: faker.helpers.arrayElement(['active', 'inactive']),
    role: faker.helpers.arrayElement(['admin', 'user']),
    createdAt: faker.date.past().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
  }
}

/** 模拟用户列表接口 */
export function mockUserList(query: any): UserListData {
  const { page = 1, pageSize = 20 } = query
  const total = 100
  const list = Array.from({ length: pageSize }, () => generateMockUser())

  return {
    list,
    total,
    page,
    pageSize,
  }
}
```

## API 测试规范

### API 接口测试
```typescript
// tests/api/user.test.ts

import { describe, expect, it, beforeEach } from 'vitest'
import { getUserList, createUser } from '@/modules/user/api/user'

describe('User API', () => {
  beforeEach(() => {
    // 设置测试环境
  })

  it('should fetch user list successfully', async () => {
    const query = { page: 1, pageSize: 10 }
    const response = await getUserList(query)

    expect(response.data).toBeDefined()
    expect(response.data.list).toBeInstanceOf(Array)
    expect(response.data.total).toBeTypeOf('number')
  })

  it('should create user successfully', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: '123456',
      role: 'user' as const
    }

    const response = await createUser(userData)

    expect(response.data).toBeDefined()
    expect(response.data.username).toBe(userData.username)
  })
})
```

## API 导出规范

### 模块 API 导出
```typescript
// src/modules/user/api/index.ts

// 导出所有 API 函数
export * from './user'

// 导出类型定义
export type * from './types'

// 导出常量
export * from './constants'
```

### 全局 API 导出
```typescript
// src/api/index.ts

// 导出 HTTP 客户端
export { http } from '@shencom/request'
export { axios } from './axios'

// 导出通用类型
export type * from './types/common'

// 导出各模块 API
export * from './modules/auth'
export * from './modules/user'
export * from './modules/system'
```
