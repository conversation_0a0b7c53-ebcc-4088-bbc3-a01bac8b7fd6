---
type: "manual"
---

# Vue 组件开发规范

基于项目的组件开发模板和最佳实践，所有组件开发必须遵循以下规范。

## 组件模板参考

以下是标准的组件开发模板，包含完整的结构和注释：

```vue
<route>
{
  meta: {
    title: "组件标题",
    requiresAuth: true
  }
}
</route>

<i18n>
{
  "zh-CN": {
    "title": "组件标题",
    "description": "组件描述"
  },
  "en": {
    "title": "Component Title",
    "description": "Component Description"
  }
}
</i18n>

<script setup lang="ts">
/**
 * 组件名称 - 组件功能描述
 *
 * 功能特性:
 * - 特性 1
 * - 特性 2
 * - 特性 3
 *
 * <AUTHOR>
 * @since 创建日期
 * @example
 * ```vue
 * <ComponentName
 *   :prop1="value1"
 *   :prop2="value2"
 *   @event1="handleEvent1"
 * />
 * ```
 */

// ==================== 类型定义 ====================
interface Props {
  /** 属性1描述 */
  prop1: string
  /** 属性2描述 */
  prop2?: number
  /** 属性3描述 */
  prop3?: boolean
}

interface Emits {
  /** 事件1描述 */
  'event1': [value: string]
  /** 事件2描述 */
  'event2': [data: object]
}

// ==================== Props & Emits ====================
const props = withDefaults(defineProps<Props>(), {
  prop2: 0,
  prop3: false,
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const state = reactive({
  loading: false,
  data: [],
  error: null,
})

const isVisible = ref(false)

// ==================== 计算属性 ====================
const computedValue = computed(() => {
  return state.data.length > 0
})

// ==================== 生命周期 ====================
onMounted(() => {
  initComponent()
})

// ==================== 方法定义 ====================
async function initComponent() {
  try {
    state.loading = true
    state.error = null
    await loadData()
  } catch (error) {
    state.error = error instanceof Error ? error.message : '未知错误'
  } finally {
    state.loading = false
  }
}

function handleEvent1(value: string) {
  emit('event1', value)
}

// ==================== 暴露给父组件 ====================
defineExpose({
  loadData,
  state: readonly(state),
})
</script>

<template>
  <div class="component-name">
    <!-- 加载状态 -->
    <div v-if="state.loading" class="loading">
      <el-loading />
    </div>

    <!-- 错误状态 -->
    <div v-else-if="state.error" class="error">
      <el-alert :title="state.error" type="error" show-icon />
    </div>

    <!-- 主要内容 -->
    <div v-else class="content">
      <div class="header">
        <h3 class="title">{{ $t('title') }}</h3>
        <p class="description">{{ $t('description') }}</p>
      </div>

      <div class="body">
        <!-- 内容区域 -->
        <div class="data-list">
          <div
            v-for="item in formattedData"
            :key="item.id"
            class="data-item"
            @click="handleEvent1(item.id)"
          >
            {{ item.name }}
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!computedValue" class="empty">
          <el-empty description="暂无数据" />
        </div>
      </div>

      <div class="footer">
        <el-button type="primary" @click="handleEvent2({ action: 'refresh' })">
          刷新
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.component-name {
  display: flex;
  flex-direction: column;
  height: 100%;

  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  .content {
    display: flex;
    flex-direction: column;
    flex: 1;

    .header {
      padding: 1rem;
      border-bottom: 1px solid var(--el-border-color-light);

      .title {
        margin: 0 0 0.5rem;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
    }

    .body {
      flex: 1;
      padding: 1rem;

      .data-item {
        padding: 0.75rem;
        background-color: var(--el-bg-color-page);
        border: 1px solid var(--el-border-color-light);
        border-radius: var(--el-border-radius-base);
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: var(--el-color-primary);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}
</style>
```

## 组件文件结构（强制顺序）

```vue
<route>
{
  meta: {
    title: "组件标题",
    requiresAuth: true
  }
}
</route>

<i18n>
{
  "zh-CN": {
    "title": "组件标题"
  },
  "en": {
    "title": "Component Title"
  }
}
</i18n>

<script setup lang="ts">
// 脚本逻辑
</script>

<template>
  <!-- 模板内容 -->
</template>

<style scoped lang="scss">
/* 样式定义 */
</style>
```

## 组件内部结构规范

### script setup 部分的组织顺序

```vue
<script setup lang="ts">
/**
 * 组件文档注释（必须）
 */

// ==================== 类型定义 ====================
interface Props {
  // Props 定义
}

interface Emits {
  // Events 定义
}

// ==================== Props & Emits ====================
const props = withDefaults(defineProps<Props>(), {
  // 默认值
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const state = reactive({
  // 响应式状态
})

const refData = ref()

// ==================== 计算属性 ====================
const computedValue = computed(() => {
  // 计算逻辑
})

// ==================== 生命周期 ====================
onMounted(() => {
  // 挂载逻辑
})

// ==================== 方法定义 ====================
function handleAction() {
  // 方法实现
}

// ==================== 组合式函数 ====================
// const { data, loading } = useCustomHook()

// ==================== 暴露给父组件 ====================
defineExpose({
  // 暴露的方法和属性
})
</script>
```

### Props 定义规范

```typescript
/** 组件 Props 接口 */
interface Props {
  /** 必填属性 - 用户ID */
  userId: number
  /** 可选属性 - 显示模式 */
  mode?: 'view' | 'edit'
  /** 可选属性 - 是否显示头像 */
  showAvatar?: boolean
  /** 可选属性 - 最大显示长度 */
  maxLength?: number
}

// 使用 withDefaults 设置默认值
const props = withDefaults(defineProps<Props>(), {
  mode: 'view',
  showAvatar: true,
  maxLength: 100,
})
```

### Events 定义规范

```typescript
/** 组件 Emits 接口 */
interface Emits {
  /** 用户更新事件 */
  'user-updated': [user: UserInfo]
  /** 状态变更事件 */
  'status-change': [status: string, data?: any]
  /** 确认事件 */
  'confirm': []
}

const emit = defineEmits<Emits>()

// 调用示例
function handleUpdate(user: UserInfo) {
  emit('user-updated', user)
}
```

## 组件命名规范

### 文件命名
- 组件文件使用 `PascalCase.vue`
- 复杂组件使用文件夹结构：
  ```
  UserProfile/
  ├── index.vue        # 主组件
  ├── UserAvatar.vue   # 子组件
  ├── types.ts         # 类型定义
  └── hooks.ts         # 组合式函数
  ```

### 组件内命名
- 组件名：`PascalCase`
- Props：`camelCase`
- Events：`kebab-case`
- CSS 类名：`kebab-case` 或 BEM 规范

## 模板规范

### 条件渲染
```vue
<template>
  <!-- 加载状态 -->
  <div v-if="loading" class="loading-container">
    <el-loading />
  </div>

  <!-- 错误状态 -->
  <div v-else-if="error" class="error-container">
    <el-alert :title="error" type="error" show-icon />
  </div>

  <!-- 主要内容 -->
  <div v-else class="content-container">
    <!-- 内容 -->
  </div>

  <!-- 空状态 -->
  <div v-if="!data.length" class="empty-container">
    <el-empty description="暂无数据" />
  </div>
</template>
```

### 列表渲染
```vue
<template>
  <div class="list-container">
    <div
      v-for="item in items"
      :key="item.id"
      class="list-item"
      @click="handleItemClick(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>
```

### 事件处理
```vue
<template>
  <!-- 推荐：使用具体的事件处理函数 -->
  <el-button @click="handleSubmit">提交</el-button>
  <el-button @click="handleCancel">取消</el-button>

  <!-- 避免：内联复杂逻辑 -->
  <!-- <el-button @click="isEdit = false; resetForm()">取消</el-button> -->
</template>
```

## 样式规范

### 优先使用 UnoCSS 工具类
```vue
<template>
  <!-- ✅ 推荐：使用 UnoCSS 原子类 -->
  <div class="flex flex-col p-4 bg-white rounded-lg shadow-sm">
    <div class="mb-4 flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">
        标题
      </h3>
      <el-button type="primary" size="small">
        操作
      </el-button>
    </div>
  </div>
</template>
```

### SCSS 样式规范
> 不建议使用 SCSS 样式，优先使用 UnoCSS 原子类
```vue
<style scoped lang="scss">
.user-profile {
  display: flex;
  flex-direction: column;

  // 使用 BEM 命名
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;

    &--compact {
      margin-bottom: 0.5rem;
    }
  }

  &__content {
    flex: 1;
  }

  // 状态修饰符
  &.is-loading {
    opacity: 0.6;
    pointer-events: none;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 0.5rem;
  }
}
</style>
```

## 组件复用规范

### 通用组件开发
- 放在 `src/components/` 目录
- 必须有完整的 Props 类型定义
- 必须有使用示例和文档
- 支持主题和国际化

### 业务组件开发
- 放在对应模块的 `components/` 目录
- 与具体业务逻辑耦合
- 可以直接使用模块内的 store 和 API

## 组件测试规范

### 组件必须包含的测试
```typescript
import { mount } from '@vue/test-utils'
import { describe, expect, it } from 'vitest'
import UserProfile from './UserProfile.vue'

describe('UserProfile', () => {
  it('should render user information correctly', () => {
    const wrapper = mount(UserProfile, {
      props: {
        userId: 1,
        mode: 'view'
      }
    })

    expect(wrapper.find('[data-testid="user-name"]').exists()).toBe(true)
  })

  it('should emit update event when save button clicked', async () => {
    const wrapper = mount(UserProfile, {
      props: { userId: 1, mode: 'edit' }
    })

    await wrapper.find('[data-testid="save-button"]').trigger('click')

    expect(wrapper.emitted('user-updated')).toBeTruthy()
  })
})
```

## 性能优化规范

### 响应式数据优化
```vue
<script setup lang="ts">
// ✅ 使用 shallowRef 优化大型对象
const largeData = shallowRef({})

// ✅ 使用 readonly 包装只读数据
const readonlyData = readonly(data)

// ✅ 合理使用 computed 缓存计算结果
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})
</script>
```

### 异步组件加载
```typescript
// 路由中使用异步组件
const UserProfile = () => import('@/components/UserProfile.vue')

// 组件中使用异步组件
const AsyncComponent = defineAsyncComponent(() =>
  import('@/components/HeavyComponent.vue')
)
```
