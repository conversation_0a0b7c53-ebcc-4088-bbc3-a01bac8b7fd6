---
type: "manual"
---

# 业务模块架构规范

本项目采用模块化架构，所有业务功能必须按照以下规范组织在 `src/modules` 目录下。

## 模块目录结构（必须遵循）

```
src/modules/
├── user/                    # 用户模块
│   ├── api/                # API 接口目录（必须）
│   │   ├── index.ts       # API 统一导出
│   │   ├── user.ts        # 具体API实现
│   │   └── types.ts       # API类型定义
│   ├── components/         # 模块组件目录
│   │   ├── UserProfile.vue
│   │   └── UserList.vue
│   ├── views/             # 模块页面目录
│   │   ├── index.vue      # 列表页
│   │   ├── detail.vue     # 详情页
│   │   └── edit.vue       # 编辑页
│   ├── store/             # 状态管理目录（必须）
│   │   ├── index.ts       # Store统一导出
│   │   └── user.ts        # 具体Store实现
│   ├── types/             # 类型定义目录
│   │   ├── index.ts       # 类型统一导出
│   │   └── user.ts        # 具体类型定义
│   ├── enums/             # 枚举定义目录
│   │   ├── index.ts       # 枚举统一导出
│   │   └── user.ts        # 具体枚举定义
│   ├── options/           # 选项配置定义目录
│   │   ├── index.ts       # 选项统一导出
│   │   └── user.ts        # 具体选项配置定义
│   └── index.ts           # 模块统一导出
```

## 文件命名规范

### 枚举文件 (`enums/xxxEnums.ts`) - 必须
- 所有枚举类型必须定义在模块的 `enums/xxxEnums.ts` 文件中
- 必须添加 JSDoc 注释说明枚举用途和每个值的含义

```typescript
// src/modules/user/enums/userEnums.ts

/**
 * 用户状态枚举
 */
export enum UserStatus {
  /** 激活状态 */
  ACTIVE = 'active',
  /** 非激活状态 */
  INACTIVE = 'inactive',
  /** 已封禁 */
  BANNED = 'banned'
}

/**
 * 用户角色枚举
 */
export enum UserRole {
  /** 超级管理员 */
  SUPER_ADMIN = 'super_admin',
  /** 管理员 */
  ADMIN = 'admin',
  /** 普通用户 */
  USER = 'user'
}
```

### Options 文件 (`xxxOptions.ts`) - 必须
- 所有选项类型必须定义在对应模块目录下的 `options/xxxOptions.ts` 文件中
- 必须添加注释说明选项用途

```typescript
// src/modules/user/options/userOptions.ts

/**
 * 用户状态选项
 */
export const userStatusOptions = [
  { label: '激活', value: 'active' },
  { label: '非激活', value: 'inactive' },
  { label: '已封禁', value: 'banned' }
] as const

/**
 * 用户角色选项
 */
export const userRoleOptions = [
  { label: '超级管理员', value: 'super_admin' },
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' }
] as const
```

### API 目录结构 - 必须
- 所有 API 接口必须定义在 `src/modules/xxx/api` 目录下
- 每个 API 文件必须有对应的类型定义

```typescript
// src/modules/user/api/user.ts

import type { UserInfo, UserListQuery, CreateUserData } from './types'

/**
 * 获取用户列表
 */
export function getUserList(query: UserListQuery) {
  return http.get<UserInfo[]>('/api/users', { params: query })
}

/**
 * 创建用户
 */
export function createUser(data: CreateUserData) {
  return http.post<UserInfo>('/api/users', data)
}
```

### Store 目录结构 - 必须
- 所有 store 模块必须定义在 `src/modules/xxx/store` 目录下
- 必须在 `index.ts` 中统一导出

```typescript
// src/modules/user/store/user.ts

export const useUserStore = defineStore('user', () => {
  const users = ref<UserInfo[]>([])
  const loading = ref(false)

  const fetchUsers = async (query: UserListQuery) => {
    loading.value = true
    try {
      const { data } = await getUserList(query)
      users.value = data
    } finally {
      loading.value = false
    }
  }

  return { users, loading, fetchUsers }
})

// src/modules/user/store/index.ts
export { useUserStore } from './user'
```

### 类型定义目录 (`types/`)
- 根据情况放置在 `src/modules/xxx/types` 目录
- 复杂类型定义单独文件，简单类型可合并

```typescript
// src/modules/user/types/user.ts

/** 用户信息 */
export interface UserInfo {
  id: number
  name: string
  email: string
  status: UserStatus
  role: UserRole
  createdAt: string
  updatedAt: string
}

/** 用户查询参数 */
export interface UserListQuery {
  page?: number
  pageSize?: number
  keyword?: string
  status?: UserStatus
}
```

## 模块导出规范

### 主入口文件 (`index.ts`)
每个模块必须有统一的导出入口：

```typescript
// src/modules/user/index.ts

// API
export * from './api'

// Store
export * from './store'

// Types
export * from './types'

// Enums
export * from './enums'

// Options
export * from './options'

// Components (可选)
export { default as UserProfile } from './components/UserProfile.vue'
export { default as UserList } from './components/UserList.vue'
```

## 模块依赖规则

- 模块间不允许直接引用，必须通过模块导出的公共接口
- 共享的类型、工具函数应放在 `src/types/` 或 `src/utils/` 目录
- 避免循环依赖

## 创建新模块检查清单

- [ ] 创建基础目录结构
- [ ] 添加 `enums/xxxEnums.ts` 文件并定义必要枚举
- [ ] 添加 `options/xxxOptions.ts` 文件并定义选项
- [ ] 创建 `api/` 目录并实现接口
- [ ] 创建 `store/` 目录并实现状态管理
- [ ] 创建 `types/` 目录并定义类型
- [ ] 添加 `index.ts` 统一导出文件
- [ ] 在路由中注册模块页面
