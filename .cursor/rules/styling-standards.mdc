---
globs: *.vue,*.scss,*.css,src/assets/**/*
---

# 样式开发规范

本项目强制使用 UnoCSS/TailwindCSS 原子类作为主要样式解决方案，SCSS 仅用于复杂样式组织。

## 样式优先级（必须遵循）

1. **首选：UnoCSS 原子类** - 用于所有基础样式
2. **次选：Element Plus 组件样式** - 使用内置组件样式
3. **最后：SCSS 模块** - 仅用于复杂交互样式和动画
4. **css变量** - 使用 `themes/index.ts` 中的 css 变量，部分已经添加到 `class` 中，如 `text-background` `text-foreground` `text-primary` `text-primary-foreground` `text-secondary` `text-destructive` `text-muted` `text-accent` `text-popover` `text-card` `bg-background` `bg-foreground` `bg-primary` `bg-primary-foreground` `bg-secondary` `bg-destructive` `bg-muted` `bg-accent` `bg-popover` `bg-card`

## UnoCSS 原子类规范

## 其他自定义规范
### 样式开发规范
#### UnoCSS 优先原则
- **禁止使用 `<style>` 标签**：所有样式必须使用 UnoCSS 原子类实现
- **优先使用 UnoCSS 预定义变量**：
  - 颜色：使用 UnoCSS 预定义的颜色变量，如 `text-gray-600`、`bg-blue` 等
  - 间距：使用 UnoCSS 间距系统，如 `p-4`、`m-2`、`gap-4` 等
  - 字体：使用 UnoCSS 字体系统，如 `text-sm`、`font-medium` 等
  - 阴影：使用 UnoCSS 阴影系统，如 `shadow-sm`、`shadow-lg` 等
- **响应式设计**：使用 UnoCSS 响应式前缀，如 `md:flex`、`lg:grid-cols-3` 等
- **状态样式**：使用 UnoCSS 状态前缀，如 `hover:bg-blue-600`、`focus:ring-2` 等
- **hover**：使用 `hover` 请参考下面规范

#### 颜色规范

- 禁止自定义颜色值，不允许使用自定义的十六进制颜色值或 RGB 值
- 优先使用下面 `class`

```
[border,input,ring,background,foreground,primary,primary-foreground,secondary,secondary-foreground,destructive,destructive-foreground,muted,muted-foreground,accent,accent-foreground,popover,popover-foreground,card,card-foreground]
```

如: `text-background` `text-foreground` `text-primary` `text-primary-foreground` `text-secondary` `text-destructive` `text-muted` `text-accent` `text-popover` `text-card` `bg-background` `bg-foreground` `bg-primary` `bg-primary-foreground` `bg-secondary` `bg-destructive` `bg-muted` `bg-accent` `bg-popover` `bg-card`

#### hover 规范

- ❌错误示范
```html
<div class="flex bg-background p-3 hover:border-red-200 hover:bg-red/10 hover:shadow-sm"></div>
```

- ✅正确示范
```html
<div hover="border-red-200 bg-red/10 shadow-sm" class="flex bg-background p-3"></div>
```

### 布局类使用
```vue
<template>
  <!-- ✅ 推荐：使用 UnoCSS 布局类 -->
  <div class="flex flex-col h-full">
    <!-- 头部 -->
    <div class="flex items-center justify-between p-4 bg-white border-b border-gray-200">
      <h1 class="text-xl font-semibold text-gray-900">
        标题
      </h1>
      <div class="flex items-center space-x-2">
        <button class="px-3 py-1 text-sm bg-blue text-white rounded hover:bg-blue-600">
          操作
        </button>
      </div>
    </div>

    <!-- 主体内容 -->
    <div class="flex-1 p-4 overflow-auto">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="p-4 bg-white rounded-lg shadow-sm border border-gray-100">
          卡片内容
        </div>
      </div>
    </div>

    <!-- 底部 -->
    <div class="flex items-center justify-end p-4 bg-gray-50 border-t border-gray-200">
      <div class="flex space-x-2">
        <button class="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50">
          取消
        </button>
        <button class="px-4 py-2 bg-blue text-white rounded hover:bg-blue-600">
          确认
        </button>
      </div>
    </div>
  </div>
</template>
```

### 常用样式类组合
```vue
<template>
  <!-- 卡片样式 -->
  <div class="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
    内容
  </div>

  <!-- 表单项样式 -->
  <div class="mb-4">
    <label class="block text-sm font-medium text-gray-700 mb-1">
      标签
    </label>
    <input class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue focus:border-transparent">
  </div>

  <!-- 按钮样式 -->
  <button class="inline-flex items-center px-4 py-2 bg-blue text-white text-sm font-medium rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
    <svg class="w-4 h-4 mr-2" fill="currentColor">
      <!-- 图标 -->
    </svg>
    按钮文字
  </button>

  <!-- 徽章样式 -->
  <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
    成功
  </span>

  <!-- 分隔符 -->
  <div class="border-t border-gray-200 my-4"></div>

  <!-- 列表项 -->
  <div class="flex items-center p-3 hover:bg-gray-50 rounded-md cursor-pointer">
    <div class="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
      <span class="text-blue-600 font-medium">A</span>
    </div>
    <div class="ml-3 flex-1">
      <p class="text-sm font-medium text-gray-900">用户名</p>
      <p class="text-sm text-gray">用户描述</p>
    </div>
  </div>
</template>
```

### 响应式设计
```vue
<template>
  <!-- 响应式网格 -->
  <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
    <div class="col-span-1 sm:col-span-2 md:col-span-1">内容</div>
  </div>

  <!-- 响应式隐藏/显示 -->
  <div class="block md:hidden">移动端显示</div>
  <div class="hidden md:block">桌面端显示</div>

  <!-- 响应式间距 -->
  <div class="p-2 sm:p-4 md:p-6 lg:p-8">
    内容
  </div>

  <!-- 响应式文字大小 -->
  <h1 class="text-lg sm:text-xl md:text-2xl lg:text-3xl font-bold">
    响应式标题
  </h1>
</template>
```

### 状态类使用
```vue
<template>
  <!-- 悬停状态 -->
  <div class="bg-white hover:bg-gray-50 hover:shadow-md transition-all duration-200">
    悬停效果
  </div>

  <!-- 焦点状态 -->
  <input class="border border-gray-300 focus:border-blue focus:ring-2 focus:ring-blue-200 focus:outline-none">

  <!-- 活跃状态 -->
  <button class="bg-blue active:bg-blue-700 text-white">
    点击效果
  </button>

  <!-- 禁用状态 -->
  <button class="bg-gray-300 text-gray cursor-not-allowed" disabled>
    禁用按钮
  </button>

  <!-- 条件状态 -->
  <div :class="[
    'p-4 rounded-lg',
    isActive ? 'bg-blue-100 border-blue-300' : 'bg-gray-100 border-gray-300',
    isError ? 'border-red-300 bg-red-50' : ''
  ]">
    条件样式
  </div>
</template>
```

## SCSS 使用规范（限制使用）

### 何时使用 SCSS
- 复杂的动画效果
- 深度样式覆盖（谨慎使用）
- 复杂的伪元素样式
- 与第三方组件的样式集成

### BEM 命名规范
```scss
// ✅ 使用 BEM 命名方法论
.user-profile {
  // 基础样式尽量使用 UnoCSS 类名替代

  // 元素
  &__avatar {
    position: relative;

    // 修饰符
    &--large {
      width: 4rem;
      height: 4rem;
    }

    &--small {
      width: 2rem;
      height: 2rem;
    }
  }

  &__info {
    // 状态
    &.is-loading {
      opacity: 0.6;
      pointer-events: none;
    }

    &.is-error {
      color: #ef4444;
    }
  }

  // 复杂动画（无法用原子类实现）
  &__loading-spinner {
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    animation: spin 1s linear infinite;
  }
}
```

### 深度样式覆盖
```vue
<style scoped lang="scss">
// ❌ 避免：随意使用深度选择器
// :deep(.el-table) { }

// ✅ 推荐：明确的深度样式覆盖，添加注释说明原因
.custom-table {
  // 覆盖 Element Plus 表格样式以适配设计要求
  :deep(.el-table__header) {
    background-color: #f8fafc;
  }

  // 覆盖表格行悬停效果
  :deep(.el-table__row:hover) {
    background-color: #f1f5f9;
  }
}
</style>
```

## Shadcn 样式规范

### 颜色规范

> 参考 `themes/index.ts` 中的 css 变量
```css
--background
--foreground
--card
--card-foreground
--popover
--popover-foreground
--primary
--primary-foreground
--secondary
--secondary-foreground
--muted
--muted-foreground
--accent
--accent-foreground
--destructive
--destructive-foreground
--border
--input
--ring
```

## Element Plus 样式集成

### 使用 Element Plus 变量
```vue
<template>
  <!-- ✅ 推荐：结合 Element Plus 和 原子类 -->
  <el-card class="!border-gray-200 !shadow-sm">
    <template #header>
      <div class="flex items-center justify-between">
        <span class="text-lg font-semibold text-gray-900">卡片标题</span>
        <el-button type="primary" size="small">操作</el-button>
      </div>
    </template>

    <div class="space-y-4">
      <p class="text-gray-600">卡片内容</p>
    </div>
  </el-card>
</template>
```

### 自定义 Element Plus 主题
```scss
// src/assets/styles/element-plus-custom.scss

// 使用 CSS 变量覆盖 Element Plus 主题
:root {
  --el-color-primary: #3b82f6;
  --el-color-success: #10b981;
  --el-color-warning: #f59e0b;
  --el-color-danger: #ef4444;
  --el-color-info: #6b7280;

  --el-border-radius-base: 0.5rem;
  --el-border-radius-small: 0.375rem;
}
```

## 颜色使用规范

### 使用语义化颜色
```vue
<template>
  <!-- ✅ 推荐：使用语义化颜色名称 -->
  <div class="text-gray-900 bg-background border-border">主要内容</div>
  <div class="text-gray bg-gray-50">次要内容</div>
  <div class="text-blue bg-blue-50">信息提示</div>
  <div class="text-green bg-green-50">成功状态</div>
  <div class="text-yellow bg-yellow-50">警告状态</div>
  <div class="text-red bg-red-50">错误状态</div>

  <!-- ❌ 避免：使用具体的颜色值 -->
  <!-- <div class="text-#333333 bg-#ffffff">内容</div> -->
</template>
```

### 暗色模式支持
```vue
<template>
  <!-- 支持暗色模式的样式 -->
  <div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
    自适应暗色模式
  </div>

  <div class="border border-gray-200 dark:border-gray-700">
    边框自适应
  </div>
</template>
```

## 动画和过渡

### 使用 UnoCSS 过渡类
```vue
<template>
  <!-- 基础过渡 -->
  <div class="transition-all duration-200 ease-in-out hover:scale-105">
    缩放动画
  </div>

  <!-- 透明度过渡 -->
  <div class="transition-opacity duration-300" :class="{ 'opacity-0': !visible, 'opacity-100': visible }">
    淡入淡出
  </div>

  <!-- 变形过渡 -->
  <div class="transition-transform duration hover:rotate-180">
    旋转动画
  </div>
</template>
```

### 复杂动画使用 SCSS
```vue
<style scoped lang="scss">
// 复杂动画效果
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter-from {
  transform: translateX(20px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-20px);
  opacity: 0;
}

// 加载动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
</style>
```

## 样式组织规范

### 全局样式结构
```
src/assets/styles/
├── design-tokens/        # 设计令牌
│   ├── colors.css       # 颜色定义
│   ├── typography.css   # 字体定义
│   └── spacing.css      # 间距定义
├── components/          # 组件样式
├── utilities/           # 工具类
├── globals.css          # 全局样式
└── element-ui.css       # Element Plus 自定义
```

### 样式文件导入顺序
```vue
<style scoped lang="scss">
// 1. 导入设计令牌
@use '@/assets/styles/design-tokens' as *;

// 2. 导入工具函数
@use '@/assets/styles/utilities' as *;

// 3. 组件特定样式
.component-name {
  // 样式定义
}
</style>
```

## 样式检查清单

开发时必须检查：
- [ ] 优先使用 UnoCSS 原子类
- [ ] 避免内联样式
- [ ] 使用语义化的颜色名称
- [ ] 支持响应式设计
- [ ] 考虑暗色模式适配
- [ ] 使用合适的过渡动画
- [ ] 遵循 BEM 命名规范（SCSS）
- [ ] 避免不必要的深度选择器
- [ ] 确保样式的可维护性
