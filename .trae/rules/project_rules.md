# 项目开发规则
> 项目开发时，请参考下面的规范，保持项目的一致性。

## API 规范
> API 规范定义了项目中使用的 API 接口，包括接口的请求参数、响应格式、错误码等。
[API 规范](docs/rules/api-standards.md)

## 代码规范
> 代码规范定义了项目中使用的编码规范，包括缩进、命名规范、注释规范等。
[代码规范](docs/rules/code-standards.md)

## 组件规范
> 组件规范定义了项目中使用的组件规范，包括组件的使用方法、组件的参数、组件的事件等。
[组件规范](docs/rules/component-guidelines.md)

## 依赖规范
> 依赖规范定义了项目中使用的依赖规范，包括依赖的版本、依赖的使用方法等。
[依赖规范](docs/rules/dependencies-guide.md)

## 模块规范
> 模块规范定义了项目中使用的模块规范，包括模块的使用方法、模块的参数、模块的事件等。
[模块规范](docs/rules/module-architecture.md)

## 项目规范
> 项目规范定义了项目的结构、文件命名、目录组织等。
[项目规范](docs/rules/project-structure.md)

## 样式规范
> 样式规范定义了项目中使用的样式规范，包括样式的使用方法、样式的参数、样式的事件等。
[样式规范](docs/rules/styling-standards.md)

## 类型规范
> 类型规范定义了项目中使用的类型规范，包括类型的使用方法、类型的参数、类型的事件等。
[类型规范](docs/rules/typescript-standards.md)

## 其他自定义规范
### 样式开发规范
#### UnoCSS 优先原则
- **禁止使用 `<style>` 标签**：所有样式必须使用 UnoCSS 原子类实现
- **优先使用 UnoCSS 预定义变量**：
  - 颜色：使用 UnoCSS 预定义的颜色变量，如 `text-gray-600`、`bg-blue-500` 等
  - 间距：使用 UnoCSS 间距系统，如 `p-4`、`m-2`、`gap-4` 等
  - 字体：使用 UnoCSS 字体系统，如 `text-sm`、`font-medium` 等
  - 阴影：使用 UnoCSS 阴影系统，如 `shadow-sm`、`shadow-lg` 等
- **响应式设计**：使用 UnoCSS 响应式前缀，如 `md:flex`、`lg:grid-cols-3` 等
- **状态样式**：使用 UnoCSS 状态前缀，如 `hover:bg-blue-600`、`focus:ring-2` 等
- **hover**：使用 `hover` 请参考下面规范

#### 颜色规范

- 禁止自定义颜色值，不允许使用自定义的十六进制颜色值或 RGB 值
- 优先使用下面 `class`

```
[border,input,ring,background,foreground,primary,primary-foreground,secondary,secondary-foreground,destructive,destructive-foreground,muted,muted-foreground,accent,accent-foreground,popover,popover-foreground,card,card-foreground]
```

如: `text-background` `text-foreground` `text-primary` `text-primary-foreground` `text-secondary` `text-destructive` `text-muted` `text-accent` `text-popover` `text-card` `bg-background` `bg-foreground` `bg-primary` `bg-primary-foreground` `bg-secondary` `bg-destructive` `bg-muted` `bg-accent` `bg-popover` `bg-card`

#### hover 规范

- ❌错误示范
```html
<div class="flex bg-background p-3 hover:border-red-200 hover:bg-red/10 hover:shadow-sm"></div>
```

- ✅正确示范
```html
<div hover="border-red-200 bg-red/10 shadow-sm" class="flex bg-background p-3"></div>
```

### 代码规范
每次完成代码编写，务必执行代码格式化操作，可以不进行 eslint 修复，只需要执行命令即可
```sh
npx eslint "src/modules/**/*.{vue,ts}" --cache --fix && npx stylelint \"src/modules/**/*.{css,scss,vue}\" --cache --fix
```

### 项目 API 规范

#### API 目录结构
API路径目录在 `src/modules/shenanPioneer/api` 下，每个模块对应一个文件，文件名为模块名，文件内容为模块的 API 定义。

```
src/modules/shenanPioneer/api/
├── index.ts          # 统一导出文件
├── types.ts          # 类型定义文件
├── config.ts         # 配置文件
├── user.ts           # 用户模块
├── ...               # 其他
```

#### API 接口对接标准流程

##### 1. 类型定义 (types.ts)
首先在 `types.ts` 中定义接口相关的类型：
```ts
// 请求参数类型
export interface ViolationTrendParams {
  startDate: string
  endDate: string
  regionCode?: string
}

// 响应数据类型
export interface ViolationTrendItem {
  date: string
  count: number
  type: string
}
```

##### 2. API 函数实现
在对应模块文件中实现 API 函数：
```ts
import type { ViolationTrendItem, ViolationTrendParams } from './types'
// aiot.ts
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 获取违规趋势数据
 */
export async function fetchViolationTrend(params: ViolationTrendParams): Promise<ViolationTrendItem[]> {
  return http.post(`${url}/aiot/violation/trend`, params)
}
```

每个函数应该使用 `import { url } from './config'`, 中的 `url` 作为接口前缀

##### 3. 统一导出 (index.ts)
在 `index.ts` 中按字母顺序导出所有 API：
```ts
// 按字母顺序导出模块
export * from './aiot'
export * from './report'
export * from './sporadic'
export * from './types'
```

##### 4. 组件中使用 API
在 Vue 组件中正确使用 API：
```ts
// 导入 API 和类型
import type { ViolationTrendItem, ViolationTrendParams } from '@shenanPioneer/api'
import { fetchViolationTrend } from '@shenanPioneer/api'

// 定义响应式数据
const loading = ref(false)
const requestParams = ref<ViolationTrendParams>({
  startDate: '',
  endDate: ''
})

// 数据获取函数
async function getData() {
  loading.value = true
  try {
    const data = await fetchViolationTrend(requestParams.value)
    // 数据转换和处理
    processData(data)
  }
  catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  }
  finally {
    loading.value = false
  }
}
```

#### 组件 API 集成最佳实践

##### 1. 加载状态管理
- 必须添加 `loading` 状态
- 在请求期间禁用相关交互元素
- 显示加载动画提升用户体验

```ts
const loading = ref(false)

// 模板中使用
<el-select :disabled="loading">
<div v-loading="loading">
```

##### 2. 错误处理
- 使用 try-catch 包装 API 调用
- 提供用户友好的错误提示
- 记录错误日志便于调试

```ts
try {
  const data = await fetchXxxx()
}
catch (error) {
  console.error('API 调用失败:', error)
  ElMessage.error('获取数据失败，请稍后重试')
}
```

##### 3. 数据转换
- API 返回数据通常需要转换为组件所需格式
- 在专门的转换函数中处理数据映射
- 保持数据转换逻辑的清晰和可维护

```ts
function transformApiData(apiData: ApiResponse[]): ChartData[] {
  return apiData.map(item => ({
    name: item.regionName,
    value: item.projectCount,
    // 其他字段映射
  }))
}
```

##### 4. 参数联动
- 当筛选条件变化时，自动重新获取数据
- 在参数变化的处理函数中调用数据获取函数

```ts
function handleDateChange(value: [string, string]) {
  requestParams.value.startDate = value[0]
  requestParams.value.endDate = value[1]
  fetchData() // 重新获取数据
}
```

##### 5. 生命周期管理
- 在 `onMounted` 中初始化数据
- 设置合理的默认参数
- 确保组件销毁时清理资源

```ts
onMounted(() => {
  // 获取初始数据
  fetchData()
})
```

#### 导入使用规范
使用的时候，在import的时候，需要添加模块名，例如：
```ts
import { fetchProjectStatistics } from '@shenanPioneer/api'
```
在使用 `import { http } from '@shencom/request'` 工具请求的时候
可以参考 `node_modules/@shencom/request/README.md` 使用说明

#### 请求结果规范
> 在 `http` 中已经定义了请求结果的规范，包括成功和失败的情况。
- ✅正确示范
```ts
async function getData() {
  try {
    const res = await fetchProjectStatistics()
    // 成功
  }
  catch (error) {
    // 失败
  }
}
```
- ❌错误示范
```ts
async function getData() {
  try {
    const res = await fetchProjectStatistics()
    if (res.errcode === '0000') {
      // 成功
    }
    else {
      // 失败
    }
  }
  catch (error) {
    // 异常
  }
}
```

#### 命名规范
- API 函数名使用动词开头，如 `fetchViolationTrend`、`fetchProjectStatistics`
- 类型名使用名词，请求参数以 `Params` 结尾，响应数据以 `Item` 或具体业务名结尾
- 文件名使用小写，多个单词用连字符分隔或驼峰命名

### 工具类规范

#### 时间处理
优先使用 `@shencom/utils` 中的 `Dayjs` 进行时间处理，不要使用 `Date` 原生方法。
在 `@shencom/utils` 中提供了 `FormatDate`、`FormatDateTime` 等工具函数，优先使用这些函数进行时间格式化。
具体文档请参考 `node_modules/@shencom/utils-date/README.md`

#### 数据格式化

在 `@shencom/utils-formatter` 中提供了 `BankCard`、`HidePhoneNumber` 等工具函数，优先使用这些函数进行数据格式化。
具体使用请参考: `node_modules/@shencom/utils-formatter/README.md`
