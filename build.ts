import { spawnSync } from 'node:child_process'
import process from 'node:process'
import upload from '@shencom/oss-upload'
import minimist from 'minimist'
import { loadEnv } from 'vite'

const args = minimist(process.argv.slice(2))
const mode = args.mode

const env = loadEnv(mode, process.cwd())

const ossPath = env.VITE_OSS_CODE_PATH

async function build() {
  if (!env.VITE_APP_PROJECT_NAME) {
    console.error('.env VITE_APP_PROJECT_NAME is not set')
    process.exit(1)
  }

  if (!upload.isUpload) {
    const { status } = spawnSync('npx', ['vite', 'build', '--mode', mode], {
      stdio: 'inherit',
    })

    if (status !== 0) {
      console.error('build failed')
      process.exit(1)
    }
  }

  const oss = new upload.OSS({
    ossPath,
    ignore: ['**/assets/**', '**/browser_upgrade/**'],
    deleteIgnore: ['**/old/**'], // 嵌套的旧平台文件不可删除
  })
  oss.upload({
    dirPath: mode === 'production' ? 'dist' : 'dist-test',
    isCloseConfirm: true,
  })
}
build()
