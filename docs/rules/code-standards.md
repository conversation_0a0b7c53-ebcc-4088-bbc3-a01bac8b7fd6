# 代码规范标准

本项目基于 Vue 3 + TypeScript，使用现代化的开发规范和工具链。

## 编程风格基础

### TypeScript/JavaScript 规范
- 使用 `@antfu/eslint-config` 作为基础配置
- 使用 2 空格缩进
- 语句末尾不使用分号
- 字符串使用单引号
- 对象和数组使用尾随逗号
- 强制使用花括号
- 避免 `console.log`（开发环境警告）

### Vue 组件结构顺序（强制执行）
```vue
<route>
<!-- 路由元信息 -->
</route>

<i18n>
<!-- 国际化配置 -->
</i18n>

<script setup lang="ts">
// 脚本逻辑
</script>

<template>
  <!-- 模板内容 -->
</template>

<style scoped lang="scss">
/* 样式定义 */
</style>
```

## 命名约定

### 文件和目录命名
- 组件文件: `PascalCase.vue` (如 `UserProfile.vue`)
- 页面目录: `kebab-case` (如 `user-management/`)
- 工具函数: `camelCase.ts` (如 `formatDate.ts`)
- 常量文件: `SCREAMING_SNAKE_CASE` 或 `camelCase`

### 变量和函数命名
- 变量: `camelCase` (如 `userName`, `isLoggedIn`)
- 常量: `SCREAMING_SNAKE_CASE` (如 `API_BASE_URL`, `MAX_RETRY_COUNT`)
- 函数: `camelCase` + 动词开头 (如 `getUserInfo`, `handleUserLogin`)
- 事件处理: `on` + 动作 (如 `onSubmit`, `onChangePassword`)
- 类: `PascalCase` (如 `UserService`, `ApiClient`)
- 接口: `PascalCase` (如 `UserInfo`, `ApiResponse`)

### Vue 组件命名
- 组件名: `PascalCase`
- Props: `camelCase`
- Events: `kebab-case`
- 插槽: `kebab-case`

## 注释标准

### 函数注释使用 JSDoc
```typescript
/**
 * 获取用户信息
 * @param userId - 用户ID
 * @param includeProfile - 是否包含详细资料
 * @returns Promise<UserInfo> 用户信息对象
 * @throws {ApiError} 当用户不存在时抛出错误
 * @example
 * ```typescript
 * const user = await getUserInfo(123, true)
 * console.log(user.name)
 * ```
 */
```

### 组件注释
```vue
<script setup lang="ts">
/**
 * 用户资料组件
 *
 * 功能:
 * - 显示用户基本信息
 * - 支持头像上传
 * - 实时保存修改
 *
 * <AUTHOR>
 * @since 创建日期
 */
</script>
```

### 复杂逻辑注释
对于复杂的业务逻辑，使用步骤化注释：
```typescript
// 处理用户权限验证
// 1. 检查用户是否已登录
// 2. 验证用户角色权限
// 3. 检查资源访问权限
function checkUserPermission(resource: string): boolean {
  // 步骤 1: 登录状态检查
  if (!isUserLoggedIn()) {
    return false
  }
  // 步骤 2: 角色权限验证
  // ...实现
}
```

## 代码质量要求

### 类型安全
- 所有函数必须有明确的类型声明
- 避免使用 `any` 类型，使用 `unknown` 替代
- 使用严格的 TypeScript 配置
- 优先使用 `interface` 而不是 `type` 定义对象类型

### 错误处理
- 使用 try-catch 包装可能失败的操作
- 提供有意义的错误信息
- 避免静默忽略错误

### 性能考虑
- 使用 `computed` 而不是方法进行计算
- 合理使用 `watch` 和 `watchEffect`
- 避免在模板中使用复杂计算
