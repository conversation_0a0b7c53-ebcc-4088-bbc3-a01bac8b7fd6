# 项目依赖使用指南

本项目使用现代化的技术栈，开发时应优先使用这些工具库中提供的功能，而不是编写自定义实现。

## 核心技术栈

### Vue 3 Composition API
```typescript
// ✅ 推荐：使用 Composition API
import { ref, computed, watch, onMounted } from 'vue'

export default {
  setup() {
    const count = ref(0)
    const doubled = computed(() => count.value * 2)

    watch(count, (newValue, oldValue) => {
      console.log(`Count changed from ${oldValue} to ${newValue}`)
    })

    onMounted(() => {
      console.log('Component mounted')
    })

    return { count, doubled }
  }
}
```

### Element Plus UI 组件
```vue
<template>
  <!-- ✅ 优先使用 Element Plus 组件 -->
  <el-button type="primary" @click="handleClick">
    点击
  </el-button>

  <el-table :data="tableData">
    <el-table-column prop="name" label="姓名" />
    <el-table-column prop="age" label="年龄" />
  </el-table>

  <el-pagination
    v-model:current-page="currentPage"
    v-model:page-size="pageSize"
    :page-sizes="[10, 20, 30, 50]"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total"
  />
</template>
```

## HTTP 请求库

### @shencom/request (优先使用)
```typescript
import { http } from '@shencom/request'

// ✅ 推荐：使用 @shencom/request
export async function getUserList(params: any) {
  const response = await http.get('/api/users', { params })
  return response.data
}

export async function createUser(data: any) {
  const response = await http.post('/api/users', data)
  return response.data
}
```

### @shencom/api (公司 API 客户端)
```typescript
import { ApiUserCreate, ApiUserList } from '@shencom/api'

// ✅ 当需要使用公司 API 客户端时
export async function getUserListFromApi(query: any) {
  const { data } = await ApiUserList(query)
  return data
}

export async function createUserFromApi(userData: any) {
  return await ApiUserCreate(userData)
}
```

## 状态管理 - Pinia

```typescript
// ✅ 推荐：使用 Pinia 而不是 Vuex
export const useUserStore = defineStore('user', () => {
  const user = ref(null)
  const isLoggedIn = computed(() => !!user.value)

  function login(userData: any) {
    user.value = userData
  }

  function logout() {
    user.value = null
  }

  return { user, isLoggedIn, login, logout }
})

// 在组件中使用
const userStore = useUserStore()
userStore.login({ id: 1, name: 'Admin' })
```

## 日期处理 - dayjs

```typescript
// ✅ 推荐：使用 dayjs 而不是 Date 原生方法
import dayjs from 'dayjs'

// 格式化日期
const formattedDate = dayjs(date).format('YYYY-MM-DD')

// 日期计算
const nextWeek = dayjs().add(1, 'week')
const lastMonth = dayjs().subtract(1, 'month')

// 比较日期
const isBefore = dayjs(date1).isBefore(date2)

// ✅ 优先使用 @shencom/utils 中的日期封装
import { FormatDate } from '@shencom/utils'
const formatted = FormatDate(date, 'YYYY-MM-DD')
```

## 工具函数库

### @vueuse/core (优先使用)
```typescript
// ✅ VueUse 提供了大量实用的组合式函数
import {
  useLocalStorage,
  useDebounceFn,
  useMouse,
  useBreakpoints,
  useFetch
} from '@vueuse/core'

// 响应式 localStorage
const theme = useLocalStorage('theme', 'light')

// 防抖函数
const debouncedFn = useDebounceFn((query) => {
  search(query)
}, 300)

// 获取鼠标位置
const { x, y } = useMouse()

// 检测设备断点
const { isDesktop, isMobile } = useBreakpoints({
  mobile: 640,
  tablet: 1024
})

// HTTP 请求
const { data, error, isFetching } = useFetch('https://api.example.com/data')
```

### @shencom/utils (公司工具库)
```typescript
// ✅ 优先使用公司封装的工具函数
import {
  ArrayToTree,     // 数组转树结构
  FormatDate,      // 日期格式化
  Storages,        // 存储工具
  UserInfo,        // 用户信息工具
  ValidatePhone    // 手机号验证
} from '@shencom/utils'

// 数组转树
const treeData = ArrayToTree(flatArray, {
  id: 'id',
  parentId: 'parentId',
  children: 'children'
})

// 存储操作
Storages.local.set('key', value)
const value = Storages.local.get('key')
```

### 其他工具库
```typescript
// ✅ 类名合并
import { clsx } from 'clsx'
import { twMerge } from 'tailwind-merge' // 与 UnoCSS 配合使用

const className = clsx('btn', { 'btn-disabled': isDisabled })
const twClassName = twMerge('text-blue-500', 'text-xl')

// ✅ UUID 生成
import { v4 as uuidv4 } from 'uuid'
const id = uuidv4()

// ✅ 事件总线
import mitt from 'mitt'
const emitter = mitt()
```

## 表单验证

### VeeValidate + Zod
```typescript
// ✅ 推荐：使用 VeeValidate 与 Zod 进行表单验证
import { toFormValidator } from '@vee-validate/zod'
import { z } from 'zod'

const schema = toFormValidator(
  z.object({
    name: z.string().min(1, '姓名必填'),
    email: z.string().email('邮箱格式不正确'),
    age: z.number().min(18, '年龄必须大于18岁')
  })
)

// 在组件中使用
const { handleSubmit, errors } = useForm({
  validationSchema: schema
})

const onSubmit = handleSubmit((values) => {
  console.log('表单数据:', values)
})
```

## 国际化 - Vue I18n

```typescript
// ✅ 推荐：使用 Vue I18n 进行国际化
import { useI18n } from 'vue-i18n'

export default {
  setup() {
    const { t } = useI18n()

    return { t }
  }
}

// 在模板中使用
// {{ t('common.save') }}
// {{ t('user.welcome', { name: userName }) }}
```

## 样式和动画

### UnoCSS/TailwindCSS (强制使用)
```vue
<template>
  <!-- ✅ 强制使用原子类样式 -->
  <div class="flex flex-col p-4 bg-white rounded-lg shadow-sm">
    <h2 class="text-xl font-semibold text-gray-900 mb-4">
      标题
    </h2>
    <p class="text-gray-600 leading-relaxed">
      内容文本
    </p>
  </div>
</template>
```

### V-Wave (点击波纹效果)
```vue
<template>
  <!-- ✅ 使用 v-wave 添加点击波纹效果 -->
  <button v-wave class="px-4 py-2 bg-blue-500 text-white rounded">
    点击我
  </button>
</template>
```

## 数据处理库

### Lodash-ES
```typescript
// ✅ 推荐：使用 lodash-es 进行数据处理
import { debounce, throttle, cloneDeep, merge } from 'lodash-es'

// 防抖
const debouncedSearch = debounce(search, 300)

// 节流
const throttledScroll = throttle(handleScroll, 100)

// 深拷贝
const clonedData = cloneDeep(originalData)

// 合并对象
const mergedConfig = merge(defaultConfig, userConfig)
```

### Scule (字符串处理)
```typescript
// ✅ 使用 scule 进行字符串格式转换
import { camelCase, kebabCase, pascalCase } from 'scule'

const camelCased = camelCase('user-name') // userName
const kebabCased = kebabCase('userName')  // user-name
const pascalCased = pascalCase('user_name') // UserName
```

## 开发工具库

### 调试工具
```typescript
// ✅ 开发环境调试工具
import VConsole from 'vconsole'    // 移动端调试
import eruda from 'eruda'          // H5 调试工具

// 根据环境变量自动加载
if (process.env.VITE_APP_DEBUG_TOOL === 'vconsole') {
  new VConsole()
} else if (process.env.VITE_APP_DEBUG_TOOL === 'eruda') {
  eruda.init()
}
```

### Mock 数据
```typescript
// ✅ 使用 Mock.js 生成模拟数据
import Mock from 'mockjs'

const mockData = Mock.mock({
  'list|10-20': [{
    'id|+1': 1,
    'name': '@cname',
    'email': '@email',
    'avatar': '@image("200x200")',
    'createTime': '@datetime'
  }]
})
```

## 依赖使用原则

### 优先级顺序
1. **公司内部包**: `@shencom/*` 包优先使用
2. **VueUse**: Vue 相关工具函数优先使用 VueUse
3. **标准库**: 优先使用项目已集成的标准库
4. **轻量级**: 选择体积小、性能好的库
5. **维护活跃**: 选择维护活跃、文档完善的库

### 禁止使用的库
```typescript
// ❌ 禁止：不要使用这些库
// import moment from 'moment'        // 使用 dayjs 替代
// import axios from 'axios'          // 使用 @shencom/request 替代
// import jQuery from 'jquery'        // 使用 Vue 原生方法
// import underscore from 'underscore' // 使用 lodash-es 替代
```

### 添加新依赖的流程
1. 检查是否有现有的解决方案
2. 评估库的大小和性能影响
3. 确认库的维护状态和社区活跃度
4. 在团队中讨论并获得批准
5. 更新依赖文档

## 常见任务对照表

| 任务 | 推荐库 | 示例 |
|------|--------|------|
| HTTP 请求 | @shencom/request | `http.get('/api/users')` |
| 状态管理 | Pinia | `const store = useUserStore()` |
| 日期处理 | dayjs | `dayjs().format('YYYY-MM-DD')` |
| 表单验证 | VeeValidate + Zod | `toFormValidator(schema)` |
| 工具函数 | @vueuse/core | `useLocalStorage('key', value)` |
| 样式类名 | clsx | `clsx('btn', { active })` |
| 数据处理 | lodash-es | `debounce(fn, 300)` |

## 性能优化

### 按需导入
```typescript
// ✅ 推荐：按需导入减少包体积
import { debounce } from 'lodash-es'
import { useLocalStorage } from '@vueuse/core'
import { ElButton, ElTable } from 'element-plus'

// ❌ 避免：全量导入
// import * as _ from 'lodash-es'
// import * as VueUse from '@vueuse/core'
```

### 动态导入
```typescript
// ✅ 推荐：大型库使用动态导入
const loadChart = async () => {
  const { default: Chart } = await import('chart.js')
  return Chart
}
```
