# 项目结构规范

本项目采用现代化的目录组织结构和开发流程规范。

## 技术栈概览

- **前端框架**: Vue 3.5+ (Composition API)
- **开发语言**: TypeScript 5.6+
- **构建工具**: Vite 6.0+
- **UI 框架**: Element Plus 2.9+
- **CSS 框架**: UnoCSS + SCSS
- **状态管理**: Pinia 2.3+
- **路由管理**: Vue Router 4.5+
- **包管理器**: pnpm (工作区模式)
- **代码规范**: ESLint + Stylelint + Prettier
- **提交规范**: Conventional Commits + cz-git

## 根目录结构

```
shenanPioneer-admin/
├── apps/                    # 多应用架构目录
│   └── login/              # 登录应用
├── packages/               # 共享包目录 (工作区模式)
│   ├── components/         # 共享组件库
│   └── utils/             # 共享工具库
├── src/                   # 主应用源码
├── public/                # 静态资源
├── docs/                  # 项目文档
├── scripts/               # 构建脚本
├── vite/                  # Vite 配置
├── .cursor/               # Cursor 配置
│   └── rules/            # 开发规范文件
├── package.json           # 项目依赖
├── pnpm-workspace.yaml    # pnpm 工作区配置
├── vite.config.ts         # Vite 构建配置
├── tsconfig.json          # TypeScript 配置
├── uno.config.ts          # UnoCSS 配置
└── eslint.config.js       # ESLint 配置
```

## 源码目录结构 (src/)

```
src/
├── api/                   # 全局 API 接口层
│   ├── modules/          # API 模块
│   ├── types/            # API 类型定义
│   ├── axios.ts          # Axios 配置
│   └── index.ts          # API 统一导出
├── assets/               # 静态资源
│   ├── images/          # 图片资源
│   └── styles/          # 样式文件
│       ├── design-tokens/ # 设计令牌
│       ├── globals.css   # 全局样式
│       └── element-ui.css # Element Plus 自定义
├── components/           # 全局组件
│   ├── ComponentName/   # 复杂组件文件夹
│   │   ├── index.vue   # 主组件
│   │   ├── types.ts    # 类型定义
│   │   └── hooks.ts    # 组合式函数
│   └── SimpleComponent.vue # 简单组件
├── layouts/             # 布局组件
│   ├── components/      # 布局子组件
│   └── index.vue       # 主布局
├── locales/            # 国际化文件
│   ├── lang/           # 语言包
│   │   ├── zh-cn.json
│   │   ├── en.json
│   │   └── zh-tw.json
│   └── index.ts        # i18n 配置
├── modules/            # 业务模块目录（核心）
│   └── [moduleName]/   # 具体业务模块
├── router/             # 路由配置
│   ├── guards.ts       # 路由守卫
│   ├── routes.ts       # 路由定义
│   └── index.ts        # 路由实例
├── store/              # 全局状态管理
│   ├── modules/        # Store 模块
│   └── index.ts        # Store 实例
├── types/              # 全局类型定义
│   ├── global.d.ts     # 全局类型
│   ├── auto-imports.d.ts # 自动导入类型
│   └── components.d.ts  # 组件类型
├── utils/              # 工具函数
│   ├── composables/    # 组合式函数
│   └── helpers/        # 辅助函数
├── views/              # 页面组件
│   ├── [module-name]/  # 模块页面
│   └── [...all].vue    # 404 页面
├── settings.ts         # 全局配置
├── settings.default.ts # 默认配置
└── main.ts            # 应用入口
```

## 文件命名规范

### 目录命名
- **kebab-case**: 页面目录、功能模块目录
  - ✅ `user-management/`, `system-settings/`
  - ❌ `userManagement/`, `SystemSettings/`

- **PascalCase**: 组件目录
  - ✅ `UserProfile/`, `DataTable/`
  - ❌ `user-profile/`, `dataTable/`

### 文件命名
- **PascalCase.vue**: 组件文件
  - ✅ `UserProfile.vue`, `DataTable.vue`
  - ❌ `user-profile.vue`, `dataTable.vue`

- **camelCase.ts**: 工具函数、API文件
  - ✅ `formatDate.ts`, `userApi.ts`
  - ❌ `format-date.ts`, `user-api.ts`

- **kebab-case.vue**: 页面文件
  - ✅ `user-list.vue`, `system-config.vue`
  - ❌ `UserList.vue`, `systemConfig.vue`

- **lowercase**: 配置文件、常量文件
  - ✅ `index.ts`, `constants.ts`, `enums.ts`

## 多应用架构 (apps/)

```
apps/
├── login/               # 登录应用
│   ├── api/            # 登录 API
│   ├── assets/         # 登录资源
│   ├── components/     # 登录组件
│   ├── pages/          # 登录页面
│   ├── service/        # 登录服务
│   ├── utils/          # 登录工具
│   ├── App.vue         # 登录入口组件
│   ├── main.ts         # 登录入口
│   ├── router.ts       # 登录路由
│   └── package.json    # 登录依赖
└── [other-apps]/       # 其他独立应用
```

## 工作区包结构 (packages/)

```
packages/
├── components/         # 共享组件库
│   ├── src/
│   │   ├── fallback/  # 错误页面组件
│   │   ├── ScSms/     # 短信组件
│   │   └── index.ts   # 组件导出
│   ├── package.json
│   └── tsconfig.json
└── utils/             # 共享工具库
    ├── src/
    │   ├── confirm.ts  # 确认工具
    │   ├── storage.ts  # 存储工具
    │   ├── userInfo.ts # 用户信息工具
    │   └── index.ts    # 工具导出
    ├── package.json
    └── tsconfig.json
```

## 导入路径规范

### 路径别名配置
```typescript
// tsconfig.json 和 vite.config.ts 中配置
{
  "@/*": ["src/*"],
  "#/*": ["src/types/*"]
}
```

### 导入顺序规范
```typescript
// ✅ 推荐的导入顺序

// 1. Node.js 内置模块
import fs from 'node:fs'
import path from 'node:path'

// 2. 第三方库
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

// 3. 公司内部包
import { http } from '@shencom/request'
import { useUserStore } from '@shencom/utils'

// 4. 项目内部 - 工具和类型
import type { UserInfo } from '#/user'
import { formatDate } from '@/utils'

// 5. 项目内部 - 业务模块
import { getUserList } from '@/modules/user/api'
import { useUserManagement } from '@/modules/user/composables'

// 6. 相对路径导入
import UserForm from './components/UserForm.vue'
import './styles/user-profile.scss'
```

## 环境配置文件

```
.env                    # 公共环境变量
.env.local             # 本地环境变量（不提交）
.env.development       # 开发环境
.env.production        # 生产环境
.env.test             # 测试环境
```

## 构建和部署文件

```
dist/                  # 生产构建输出
dist-test/            # 测试构建输出
node_modules/         # 依赖包
.vscode/              # VSCode 配置
.cursor/              # Cursor IDE 配置
.git/                 # Git 配置
```

## 项目配置文件说明

- **package.json**: 项目依赖和脚本配置
- **pnpm-workspace.yaml**: pnpm 工作区配置
- **vite.config.ts**: Vite 构建工具配置
- **tsconfig.json**: TypeScript 编译配置
- **uno.config.ts**: UnoCSS 样式框架配置
- **eslint.config.js**: 代码规范检查配置
- **stylelint.config.js**: 样式规范检查配置
- **postcss.config.js**: PostCSS 配置

## 目录创建规则

### 创建新业务模块
1. 在 `src/modules/` 下创建模块目录
2. 按照模块架构规范创建子目录
3. 添加必要的 `index.ts` 导出文件
4. 在路由中注册模块页面

### 创建新组件
1. 简单组件直接在对应目录创建 `.vue` 文件
2. 复杂组件创建文件夹，包含 `index.vue`、`types.ts` 等
3. 全局组件放在 `src/components/`
4. 业务组件放在对应模块的 `components/` 目录

### 创建新页面
1. 在 `src/views/` 或模块的 `views/` 目录创建
2. 使用 `kebab-case` 命名页面文件
3. 复杂页面可创建文件夹包含子组件
4. 在路由配置中添加页面路由

## 最佳实践

### 目录组织原则
- **按功能分组**: 优先按业务功能组织，而非技术类型
- **就近原则**: 相关文件放在一起，减少跨目录引用
- **单一职责**: 每个目录和文件职责明确
- **可扩展性**: 考虑未来扩展，避免过度嵌套

### 文件大小控制
- 单个文件不超过 500 行代码
- 复杂组件拆分为多个子组件
- 大型工具文件拆分为多个模块
- 合理使用 `index.ts` 文件进行导出

### 依赖管理
- 使用 pnpm 工作区管理多包依赖
- 避免循环依赖
- 明确区分开发依赖和生产依赖
- 定期更新和清理无用依赖
