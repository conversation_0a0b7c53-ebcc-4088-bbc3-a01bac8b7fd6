# TypeScript 开发规范

本项目使用 TypeScript 5.6+ 进行强类型开发，所有代码必须遵循严格的类型规范。

## 基础类型规范

### 基本类型使用
```typescript
// ✅ 推荐：明确的类型声明
const name: string = 'admin'
const age: number = 25
const isActive: boolean = true
const tags: string[] = ['admin', 'user']
const user: UserInfo = { id: 1, name: 'admin' }

// ❌ 避免：使用 any 类型
// const data: any = {}

// ✅ 推荐：使用 unknown 替代 any
const data: unknown = {}
if (typeof data === 'object' && data !== null) {
  // 类型守卫后使用
}
```

### 联合类型和枚举
```typescript
// ✅ 联合类型
type Theme = 'light' | 'dark' | 'auto'
type Status = 'pending' | 'success' | 'error'

// ✅ 字符串枚举
enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  GUEST = 'guest'
}

// ✅ 数字枚举
enum Permission {
  READ = 1,
  WRITE = 2,
  DELETE = 4,
  ADMIN = READ | WRITE | DELETE
}

// ✅ const assertions
const THEMES = ['light', 'dark', 'auto'] as const
type ThemeType = typeof THEMES[number]
```

## 接口和类型定义

### Interface vs Type
```typescript
// ✅ 推荐：使用 interface 定义对象结构
interface UserInfo {
  readonly id: number
  name: string
  email: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
}

// ✅ 推荐：使用 type 定义联合类型、工具类型
type UserStatus = 'active' | 'inactive' | 'banned'
type UserWithoutId = Omit<UserInfo, 'id'>
type UserUpdate = Partial<Pick<UserInfo, 'name' | 'email' | 'avatar'>>

// ✅ 接口继承
interface AdminUser extends UserInfo {
  permissions: Permission[]
  lastLoginAt?: Date
}

// ✅ 类型交叉
type UserWithStatus = UserInfo & {
  status: UserStatus
}
```

### 泛型使用
```typescript
// ✅ 基础泛型
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  success: boolean
}

// ✅ 泛型约束
interface Repository<T extends { id: number }> {
  findById(id: number): Promise<T | null>
  create(data: Omit<T, 'id'>): Promise<T>
  update(id: number, data: Partial<T>): Promise<T>
  delete(id: number): Promise<void>
}

// ✅ 条件类型
type ApiMethod<T> = T extends 'GET'
  ? (url: string, params?: any) => Promise<any>
  : T extends 'POST'
  ? (url: string, data?: any) => Promise<any>
  : never

// ✅ 映射类型
type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>
type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>
```

## 函数类型规范

### 函数声明和类型
```typescript
// ✅ 函数类型声明
type EventHandler<T = Event> = (event: T) => void
type AsyncHandler<T, R> = (data: T) => Promise<R>

// ✅ 函数重载
function formatDate(date: Date): string
function formatDate(date: string): string
function formatDate(date: number): string
function formatDate(date: Date | string | number): string {
  // 实现
  return new Date(date).toISOString()
}

// ✅ 可选参数和默认参数
function createUser(
  name: string,
  email: string,
  role: UserRole = UserRole.USER,
  options?: {
    sendWelcomeEmail?: boolean
    generatePassword?: boolean
  }
): Promise<UserInfo> {
  // 实现
}

// ✅ 剩余参数
function combineClasses(...classes: (string | undefined | null)[]): string {
  return classes.filter(Boolean).join(' ')
}
```

### 异步函数类型
```typescript
// ✅ Promise 类型
async function fetchUserData(id: number): Promise<UserInfo> {
  const response = await fetch(`/api/users/${id}`)
  const data: ApiResponse<UserInfo> = await response.json()
  return data.data
}

// ✅ 错误处理类型
type Result<T, E = Error> = {
  success: true
  data: T
} | {
  success: false
  error: E
}

async function safeApiCall<T>(
  apiCall: () => Promise<T>
): Promise<Result<T>> {
  try {
    const data = await apiCall()
    return { success: true, data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error : new Error('Unknown error')
    }
  }
}
```

## Vue 组件类型

### Props 类型定义
```typescript
interface Props {
  /** 用户ID - 必填 */
  userId: number
  /** 显示模式 - 可选 */
  mode?: 'view' | 'edit' | 'create'
  /** 用户信息 - 可选，有默认值 */
  userInfo?: UserInfo
  /** 配置选项 - 复杂对象 */
  options?: {
    showAvatar?: boolean
    allowEdit?: boolean
    maxNameLength?: number
  }
  /** 事件处理函数 - 可选 */
  onUpdate?: (user: UserInfo) => void
}

// ✅ 使用 withDefaults
const props = withDefaults(defineProps<Props>(), {
  mode: 'view',
  userInfo: () => ({
    id: 0,
    name: '',
    email: ''
  } as UserInfo),
  options: () => ({
    showAvatar: true,
    allowEdit: false,
    maxNameLength: 50
  })
})
```

### Emits 类型定义
```typescript
interface Emits {
  /** 用户更新事件 */
  'user-updated': [user: UserInfo, changes: Partial<UserInfo>]
  /** 状态变更事件 */
  'status-changed': [status: UserStatus]
  /** 确认事件 - 无参数 */
  'confirm': []
  /** 错误事件 */
  'error': [error: Error]
}

const emit = defineEmits<Emits>()

// ✅ 类型安全的事件调用
function handleUserUpdate(user: UserInfo) {
  const changes: Partial<UserInfo> = { name: user.name }
  emit('user-updated', user, changes)
}
```

### Emits 事件命名规范

```typescript
// ✅ 推荐：事件名使用 camelCase
interface Emits {
  /** 区域变化事件 */
  (event: 'regionChange', region: string): void
}
/**
 * 处理区域变化
 * @param value - 选中的区域
 */
function handleRegionChange(value: string): void {
  emit('regionChange', value)
}

// ❌ 避免：事件名使用 kebab-case
interface Emits {
  /** 区域变化事件 */
  (event: 'region-change', region: string): void
}
function handleRegionChange(value: string): void {
  emit('region-change', value)
}
```

> 事件名统一使用 camelCase，避免 kebab-case，保持与 TypeScript 类型系统和 IDE 自动补全一致性。

### Composables 类型
```typescript
// ✅ 组合式函数类型定义
interface UseUserOptions {
  immediate?: boolean
  onError?: (error: Error) => void
}

interface UseUserReturn {
  user: Ref<UserInfo | null>
  loading: Ref<boolean>
  error: Ref<Error | null>
  fetchUser: (id: number) => Promise<void>
  updateUser: (data: Partial<UserInfo>) => Promise<void>
  resetUser: () => void
}

function useUser(options: UseUserOptions = {}): UseUserReturn {
  const user = ref<UserInfo | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const fetchUser = async (id: number) => {
    loading.value = true
    error.value = null
    try {
      user.value = await getUserById(id)
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('Fetch failed')
      options.onError?.(error.value)
    } finally {
      loading.value = false
    }
  }

  const updateUser = async (data: Partial<UserInfo>) => {
    if (!user.value) return

    try {
      user.value = await updateUserById(user.value.id, data)
    } catch (err) {
      error.value = err instanceof Error ? err : new Error('Update failed')
      throw error.value
    }
  }

  const resetUser = () => {
    user.value = null
    error.value = null
  }

  return {
    user: readonly(user),
    loading: readonly(loading),
    error: readonly(error),
    fetchUser,
    updateUser,
    resetUser
  }
}
```

## 类型守卫和断言

### 类型守卫函数
```typescript
// ✅ 类型守卫
function isString(value: unknown): value is string {
  return typeof value === 'string'
}

function isUserInfo(obj: unknown): obj is UserInfo {
  return typeof obj === 'object'
    && obj !== null
    && 'id' in obj
    && 'name' in obj
    && 'email' in obj
}

function isValidEmail(email: string): email is string {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// ✅ 使用类型守卫
function processUserData(data: unknown) {
  if (isUserInfo(data)) {
    // data 现在是 UserInfo 类型
    console.log(data.name)
  }
}
```

### 类型断言
```typescript
// ✅ 非空断言（确定不为空时使用）
const user = getUserFromCache()
const userName = user!.name // 确定 user 不为 null

// ✅ 类型断言
const element = document.getElementById('app') as HTMLDivElement

// ✅ const 断言
const config = {
  apiUrl: '/api',
  timeout: 5000
} as const
// config 的类型是 { readonly apiUrl: '/api'; readonly timeout: 5000 }
```

## 工具类型使用

### 内置工具类型
```typescript
interface User {
  id: number
  name: string
  email: string
  password: string
  createdAt: Date
}

// ✅ 内置工具类型
type PublicUser = Omit<User, 'password'> // 排除密码字段
type UserCreate = Pick<User, 'name' | 'email' | 'password'> // 只要特定字段
type UserUpdate = Partial<Pick<User, 'name' | 'email'>> // 部分字段可选
type RequiredUser = Required<User> // 所有字段必填
type UserRecord = Record<string, User> // 记录类型

// ✅ 索引类型
type UserKeys = keyof User // 'id' | 'name' | 'email' | 'password' | 'createdAt'
type UserName = User['name'] // string
```

### 自定义工具类型
```typescript
// ✅ 自定义工具类型
type NonNullable<T> = T extends null | undefined ? never : T
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}
type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

// ✅ 条件类型
type ApiEndpoint<T> = T extends 'users'
  ? '/api/users'
  : T extends 'posts'
  ? '/api/posts'
  : never

// ✅ 模板字面量类型
type EventName<T extends string> = `on${Capitalize<T>}`
type UserEvent = EventName<'userUpdate'> // 'onUserUpdate'
```

## 错误处理类型

### 错误类型定义
```typescript
// ✅ 错误基类
abstract class BaseError extends Error {
  abstract readonly code: string
  abstract readonly statusCode: number

  constructor(message: string, public readonly context?: Record<string, any>) {
    super(message)
    this.name = this.constructor.name
  }
}

// ✅ 具体错误类型
class ValidationError extends BaseError {
  readonly code = 'VALIDATION_ERROR'
  readonly statusCode = 400

  constructor(
    message: string,
    public readonly field: string,
    context?: Record<string, any>
  ) {
    super(message, context)
  }
}

class NotFoundError extends BaseError {
  readonly code = 'NOT_FOUND'
  readonly statusCode = 404
}

// ✅ 错误联合类型
type AppError = ValidationError | NotFoundError | BaseError
```

## 类型检查清单

开发时必须检查：
- [ ] 避免使用 `any` 类型，使用 `unknown` 替代
- [ ] 优先使用 `interface` 定义对象类型
- [ ] 使用 `type` 定义联合类型和工具类型
- [ ] 为函数提供完整的类型签名
- [ ] 使用类型守卫进行安全的类型检查
- [ ] 合理使用泛型提高代码复用性
- [ ] 为复杂的业务逻辑定义专门的类型
- [ ] 使用工具类型简化类型定义
- [ ] 确保异步函数有正确的返回类型
- [ ] 为错误处理定义明确的类型
- [ ] 确保异步函数有正确的返回类型
- [ ] 为错误处理定义明确的类型
