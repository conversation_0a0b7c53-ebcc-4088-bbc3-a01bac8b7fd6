<i18n>
{
  "zh-cn": {
    "fallback": {
      "pageNotFound": "哎呀！未找到页面",
      "pageNotFoundDesc": "抱歉，我们无法找到您要找的页面。",
      "forbidden": "哎呀！访问被拒绝",
      "forbiddenDesc": "抱歉，您没有权限访问此页面。",
      "internalError": "哎呀！出错了",
      "internalErrorDesc": "抱歉，服务器遇到错误。",
      "offline": "离线页面",
      "offlineError": "哎呀！网络错误",
      "offlineErrorDesc": "抱歉，无法连接到互联网，请检查您的网络连接并重试。",
      "comingSoon": "即将推出",
    },
    "common": {
      "backToHome": "返回首页",
      "refresh": "刷新",
    }
  },
  "zh-tw": {
    "fallback": {
      "pageNotFound": "哎呀！未找到页面",
      "pageNotFoundDesc": "抱歉，我们无法找到您要找的页面。",
      "forbidden": "哎呀！访问被拒绝",
      "forbiddenDesc": "抱歉，您没有权限访问此页面。",
      "internalError": "哎呀！出错了",
      "internalErrorDesc": "抱歉，服务器遇到错误。",
      "offline": "离线页面",
      "offlineError": "哎呀！网络错误",
      "offlineErrorDesc": "抱歉，无法连接到互联网，请检查您的网络连接并重试。",
      "comingSoon": "即将推出",
    },
    "common": {
      "backToHome": "返回首页",
      "refresh": "刷新",
    }
  },
  "en": {
    "fallback": {
      "pageNotFound": "Oops! Page Not Found",
      "pageNotFoundDesc": "Sorry, we couldn't find the page you were looking for.",
      "forbidden": "Oops! Access Denied",
      "forbiddenDesc": "Sorry, but you don't have permission to access this page.",
      "internalError": "Oops! Something Went Wrong",
      "internalErrorDesc": "Sorry, but the server encountered an error.",
      "offline": "Offline Page",
      "offlineError": "Oops! Network Error",
      "offlineErrorDesc": "Sorry, can't connect to the internet. Check your connection.",
      "comingSoon": "Coming Soon",
    },
    "common": {
      "backToHome": "Back To Home",
      "refresh": "Refresh",
    }
  }
}
</i18n>

<script setup lang="ts">
import type { FallbackProps } from './fallback'

import { computed, defineAsyncComponent } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'

defineOptions({ name: 'Fallback' })

const props = withDefaults(defineProps<Props>(), {
  description: '',
  homePath: '/',
  image: '',
  showBack: true,
  status: 'coming-soon',
  title: '',
})

const { t } = useI18n()

interface Props extends FallbackProps {}

const Icon403 = defineAsyncComponent(() => import('./icons/icon-403.vue'))
const Icon404 = defineAsyncComponent(() => import('./icons/icon-404.vue'))
const Icon500 = defineAsyncComponent(() => import('./icons/icon-500.vue'))
const IconHello = defineAsyncComponent(
  () => import('./icons/icon-coming-soon.vue'),
)
const IconOffline = defineAsyncComponent(
  () => import('./icons/icon-offline.vue'),
)

const titleText = computed(() => {
  if (props.title) {
    return props.title
  }

  switch (props.status) {
    case '403': {
      return t('fallback.forbidden')
    }
    case '404': {
      return t('fallback.pageNotFound')
    }
    case '500': {
      return t('fallback.internalError')
    }
    case 'coming-soon': {
      return t('fallback.comingSoon')
    }
    case 'offline': {
      return t('fallback.offlineError')
    }
    default: {
      return ''
    }
  }
})

const descText = computed(() => {
  if (props.description) {
    return props.description
  }
  switch (props.status) {
    case '403': {
      return t('fallback.forbiddenDesc')
    }
    case '404': {
      return t('fallback.pageNotFoundDesc')
    }
    case '500': {
      return t('fallback.internalErrorDesc')
    }
    case 'offline': {
      return t('fallback.offlineErrorDesc')
    }
    default: {
      return ''
    }
  }
})

const fallbackIcon = computed(() => {
  switch (props.status) {
    case '403': {
      return Icon403
    }
    case '404': {
      return Icon404
    }
    case '500': {
      return Icon500
    }
    case 'coming-soon': {
      return IconHello
    }
    case 'offline': {
      return IconOffline
    }
    default: {
      return null
    }
  }
})

const showBack = computed(() => {
  return props.status === '403' || props.status === '404'
})

const showRefresh = computed(() => {
  return props.status === '500' || props.status === 'offline'
})

const { push } = useRouter()

// 返回首页
function back() {
  push(props.homePath)
}

function refresh() {
  location.reload()
}
</script>

<template>
  <div class="size-full flex flex-col items-center justify-center duration-300">
    <img v-if="image" :src="image" class="md:1/3 w-1/2 lg:w-1/4">
    <component
      :is="fallbackIcon"
      v-else-if="fallbackIcon"
      class="md:1/3 h-1/3 w-1/2 lg:w-1/4"
    />
    <div class="flex-col-center">
      <slot v-if="$slots.title" name="title" />
      <p
        v-else-if="titleText"
        class="mt-8 text-2xl text-foreground lg:text-4xl md:text-3xl"
      >
        {{ titleText }}
      </p>
      <slot v-if="$slots.describe" name="describe" />
      <p
        v-else-if="descText"
        class="md:text-md my-4 text-muted-foreground lg:text-lg"
      >
        {{ descText }}
      </p>
      <slot v-if="$slots.action" name="action" />
      <ElButton v-else-if="showBack" type="primary" size="large" @click="back">
        <!-- <ArrowLeft class="mr-2 size-4" /> -->
        <i class="i-ep:back mr-2 text-xl" />
        {{ t('common.backToHome') }}
      </ElButton>
      <ElButton v-else-if="showRefresh" type="primary" size="large" @click="refresh">
        <!-- <RotateCw class="mr-2 size-4" /> -->
        <i class="i-ep:refresh mr-2 text-lg" />
        {{ t('common.refresh') }}
      </ElButton>
    </div>
  </div>
</template>
