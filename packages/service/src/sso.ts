import { UserInfo } from '@admin/utils'
import { ApiGetScCode, ApiScCodeLogin } from '@shencom/api'
import { UrlParam } from '@shencom/utils'

function clearUrlHref() {
  const { origin, hash, pathname } = window.location
  window.history.pushState({}, '', `${origin}${pathname}${hash}`)
}

export class ServiceSSO {
  static has() {
    return UrlParam('sccode')
  }

  static async getScCode() {
    const { data } = await ApiGetScCode()
    return data.code
  }

  static async codeLogin(code: string) {
    const { data } = await ApiScCodeLogin(code)
    if (data) {
      UserInfo.setRootInfo(data)
    }
    else {
      throw new Error('登录失败')
    }
  }

  static async autoLogin() {
    const isLogin = UserInfo.isLogin()
    const userId = UserInfo.getUid()

    const uid = UrlParam('uid')
    // 如果当前用户是登录用户，并且登录状态正常，则不进行登录
    if (uid && userId === uid && isLogin) {
      return
    }

    const sccode = UrlParam('sccode')
    if (sccode) {
      await this.codeLogin(sccode)
      clearUrlHref()
    }
  }
}
