import { IsDev, IsP<PERSON>, Storages, UserInfo } from '@admin/utils'
import { ApiRefreshToken } from '@shencom/api'

async function requestRefreshToken(token: string) {
  try {
    const { data } = await ApiRefreshToken(token, {
      headers: { Authorization: null },
    })
    return data
  }
  catch (error) {
    console.error(error)
  }
  return null
}

const { VITE_APP_30_DAY_LOGIN_ENABLE, VITE_APP_REFRESH_TOKEN } = import.meta.env

// 30天免登录状态存储key
const IS_30_DAY_LOGIN_KEY = 'IS_30_DAY_LOGIN_KEY'

class _ServiceToken {
  // 是否开启30天免登录
  enable = VITE_APP_30_DAY_LOGIN_ENABLE as boolean

  constructor() {
    this.set30DayLogin(this.get30DayLogin() ?? !IsPro)
  }

  getRefreshToken() {
    const devRefreshToken = VITE_APP_REFRESH_TOKEN as string
    if (IsDev) {
      return devRefreshToken || UserInfo.getRefreshToken()
    }

    return UserInfo.getRefreshToken()
  }

  // 存储30天免登录状态
  set30DayLogin(value: boolean) {
    Storages.setLasting(IS_30_DAY_LOGIN_KEY, value)
  }

  // 获取30天免登录状态
  get30DayLogin() {
    const value = Storages.getLasting<boolean>(IS_30_DAY_LOGIN_KEY)

    return value
  }

  /** 用 refreshToken 获取新 token，用户信息不变 */
  async login() {
    if (!IsDev) {
      if (!this.enable) {
        return false
      }

      if (!this.get30DayLogin()) {
        return false
      }
    }

    const refreshToken = this.getRefreshToken()

    if (!refreshToken) {
      return false
    }

    const data = await requestRefreshToken(refreshToken)

    if (data) {
      UserInfo.setRootInfo(data)
      UserInfo.setRefreshToken(data.refreshToken)
    }

    return !!data
  }
}

export const ServiceToken = new _ServiceToken()
