import { Storages } from '@admin/utils'
import { http } from '@shencom/request'
import { ValidateURL } from '@shencom/utils'
import { OldPlatformService } from './oldPlatform'

export interface MenuItem {
  id: string
  url?: string
  children?: MenuItem[]
  name: string
  displayName: string
  payload?: string
  icon?: string
}

interface Route {
  path: string
  name: string
  redirect?: string
  component: string
  meta: {
    id: string
    icon: string
    title: string
    cache: boolean
    admin: boolean
  }
  children?: Route[]
}

const dev = import.meta.env.DEV

function urlToPath(url: string, id: string) {
  const isUrl = ValidateURL(url)
  if (isUrl) {
    return `/${OldPlatformService.routerName}/external/${encodeURIComponent(url)}`
  }
  return `/${OldPlatformService.routerName}/${id}${url}`
}

function childrenToFirstPath(children: MenuItem[]) {
  if (!children.length) {
    return ''
  }

  const first = children[0]

  if (first?.url) {
    return urlToPath(first.url, first.id)
  }

  return childrenToFirstPath(first.children || [])
}

function parseChildren(i: MenuItem) {
  const { id, children = [], url, name, displayName, icon = 'i-ri:external-link-fill' } = i

  const res: Route = {
    component: 'Layout',
    path: '',
    name,
    meta: {
      id,
      icon,
      title: displayName,
      cache: true,
      admin: true,
    },
  }

  if (!url && !children.length) {
    res.path = `/empty/${id}`
    return res
  }

  if (url) {
    res.path = urlToPath(url, id)
  }
  else {
    const firstPath = childrenToFirstPath(children)
    res.path = `/empty/${id}`
    if (firstPath) {
      res.redirect = firstPath
    }
  }

  if (children.length) {
    res.children = children.map((j: MenuItem) => parseChildren(j))
  }

  return res
}

function formatMenuToRoute(data: MenuItem[]) {
  const menus = data.map((item: MenuItem) => ({
    meta: {
      title: item.displayName,
      icon: item.icon || 'ion:settings-sharp',
      admin: true,
    },
    children: item.children?.map((i: MenuItem) => parseChildren(i)) || [],
  }))

  return menus
}

interface Organization {
  id: string
  name: string
}

/**
 * 获取组织列表
 */
async function ApiGetOrganization() {
  const api = `/fn/rmsv3/members/type/relate/show/all/organization`
  const { data } = await http.post<Organization[]>(api, {})
  return data
}

/**
 * 不传 organizationId 时，获取上一次切换到的那个组织，如果没有就返回第一个组织
 * 传 organizationId 时，进行组织切换，保存提交的组织id
 */
export async function ApiSwitchOrganization(organizationId?: string) {
  const { data } = await http.post<Organization>(`/sys/role/user/organization/switch`, {
    organizationId,
  })
  return data
}

/**
 * 获取用户菜单树
 */
async function ApiGetMenus() {
  const body = { sorts: [{ orderField: 'sort', orderType: 'asc' }] }

  const { data } = await http.post<MenuItem[]>(`/sys/menu/tree/my`, body)
  return data
}

/** 获取用户权限列表 */
async function ApiGetPermission() {
  const { data } = await http.get<string[]>(`/sys/permission/allPermission`)
  return data
}

class CacheUserConfig {
  static getCachePermissions() {
    return Storages.getUser<string[]>('permissions') || []
  }

  static getCacheMenus() {
    return Storages.getUser<MenuItem[]>('menus') || []
  }

  protected static setCachePermissions(permissions: string[]) {
    Storages.setUser('permissions', permissions, dev ? 60 : 30)
  }

  protected static setCacheMenus(menus: MenuItem[]) {
    Storages.setUser('menus', menus, dev ? 60 : 30)
  }

  static hasCachePermissions() {
    return this.getCachePermissions().length > 0
  }

  static hasCacheMenus() {
    return this.getCacheMenus().length > 0
  }

  static removeCachePermissions() {
    Storages.removeUser('permissions')
  }

  static removeCacheMenus() {
    Storages.removeUser('menus')
  }

  static getCacheOrganization() {
    return Storages.getUser<Organization[]>('organization') || []
  }

  protected static setCacheOrganization(organization: Organization[]) {
    Storages.setUser('organization', organization, 60)
  }

  static removeCacheOrganization() {
    Storages.removeUser('organization')
  }
}

export class ServiceUser extends CacheUserConfig {
  static async requestOrganization() {
    let organization = this.getCacheOrganization()
    if (!organization?.length) {
      organization = await ApiGetOrganization()
      this.setCacheOrganization(organization)
    }
    return organization
  }

  static async requestMenus() {
    let menus = this.getCacheMenus()
    if (!menus?.length) {
      menus = await ApiGetMenus()
      this.setCacheMenus(menus)
    }
    return formatMenuToRoute(menus)
  }

  static async requestPermissions() {
    let permissions = this.getCachePermissions()
    if (!permissions?.length) {
      permissions = await ApiGetPermission()
      this.setCachePermissions(permissions)
      return permissions
    }
    return permissions
  }
}
