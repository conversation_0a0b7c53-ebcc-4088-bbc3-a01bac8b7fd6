# 待预约清单、待服务清单

1. 帮我完善接口内容
2. 不需要测试用例

## 类型和状态
```ts
enum FlowType {
  工程创建 = 0,
  安装预约 = 1,
  现场勘察 = 2,
  上门安装 = 3,
  接入监管 = 4,
  施工完成 = 5,
  回收预约 = 6,
  上门回收 = 7,
  监管结束 = 8,
}

// 0-未完成，1-已完成
enum FlowStatus {
  未完成,
  已完成,
}
```

## 待预约清单

### 原型图
链接: `prd/待预约清单.png`

### 类型说明

待预约安装：未接入监管-未开始/进行中，未提交预约安装的工程。
  {
    "flow": "0,1"
  }

待预约回收：已接入监管-已结束，未提交预约回收的工程。
  {
    "flow": "4,5"
  }

## 待服务清单

待勘查：已提交预约安装，未提交现场勘察结果的工程。
{
  "status": 0,
  "flow": "2"
}

待安装：已提交现场勘察结果，未提交上门安装结果的工程。
{
  "status": 0,
  "flow": "3"
}

待回收：已提交预约回收，未提交回收结果的工程。
{
  "status": 0,
  "flow": "7"
}

### 原型图
链接: `prd/待服务清单.png`

## 接口信息

组件放到文件: `src/modules/shenanPioneer/views/workbench/components`

url: `/monitor/flow/index`
post 请求

请求参数
```ts
{
  "size": 10,
  "page": 0,
  "status": FlowStatus,
  "flow": FlowType,
}
```

1. 注意需要分页
2. 更多按钮,保留跳转路由
3. 在 `src/modules/shenanPioneer/views/workbench/index.vue` 调用

响应数据
```json
{
  "data": {
    "content": [
      {
        "createdAt": 1753498537000,
        "flow": 3,
        "id": "1716207828469547008",
        "orderId": "1715984850716721152",
        "projectId": "1715984849731059712",
        "state": 0,
        "updatedAt": 1753498537000
      },
      {
        "createdAt": 1753497561000,
        "flow": 3,
        "id": "1716203734677848064",
        "orderId": "1715984688661397504",
        "projectId": "1715984688447488000",
        "state": 0,
        "updatedAt": 1753497561000
      },
      {
        "createdAt": 1753445678000,
        "createdUser": "1705411683269828608",
        "createdUserName": "周静",
        "createdUserPhone": "13599991255",
        "finishTime": 1753497561000,
        "flow": 2,
        "id": "1715986118969393153",
        "inspectTime": 1753497540000,
        "installCnt": 1,
        "orderId": "1715984688661397504",
        "projectId": "1715984688447488000",
        "relateId": "1716203734669459456",
        "reservationTime": 1753497540000,
        "state": 1,
        "updatedAt": 1753497561000
      },
      {
        "createdAt": 1753445654000,
        "flow": 7,
        "id": "1715986020092870657",
        "orderId": "1714504493286776832",
        "projectId": "1714504492179480576",
        "state": 0,
        "updatedAt": 1753445654000
      },
      {
        "createdAt": 1753445637000,
        "createdUser": "1705411683269828608",
        "createdUserName": "周静",
        "createdUserPhone": "13599991255",
        "finishTime": 1753498537000,
        "flow": 2,
        "id": "1715985947309113345",
        "inspectTime": 1753498500000,
        "installCnt": 1,
        "orderId": "1715984850716721152",
        "projectId": "1715984849731059712",
        "relateId": "1716207828461158400",
        "reservationTime": 1753498500000,
        "state": 1,
        "updatedAt": 1753498537000
      },
      {
        "contactMobile": "17796371752",
        "contactName": "温尚儒2",
        "createdAt": 1753445375000,
        "createdUser": "1701776612231847936",
        "createdUserName": "温尚儒",
        "createdUserPhone": "17796371751",
        "finishTime": 1753445637000,
        "flow": 1,
        "id": "1715984850720915456",
        "orderId": "1715984850716721152",
        "projectId": "1715984849731059712",
        "relateId": "1715985947309113344",
        "reservationTime": 1753923600000,
        "state": 1,
        "updatedAt": 1753445637000
      },
      {
        "createdAt": 1753445375000,
        "createdUser": "1701776612231847936",
        "createdUserName": "温尚儒",
        "createdUserPhone": "17796371751",
        "finishTime": 1753445375000,
        "flow": 0,
        "id": "1715984850716721153",
        "orderId": "1715984850716721152",
        "projectId": "1715984849731059712",
        "state": 1,
        "updatedAt": 1753445375000
      },
      {
        "createdAt": 1753445347000,
        "flow": 1,
        "id": "1715984730256310272",
        "orderId": "1715984730252115968",
        "projectId": "1715984730185007104",
        "state": 0,
        "updatedAt": 1753445347000
      },
      {
        "createdAt": 1753445347000,
        "createdUser": "1701776612231847936",
        "createdUserName": "温尚儒",
        "createdUserPhone": "17796371751",
        "finishTime": 1753445347000,
        "flow": 0,
        "id": "1715984730252115969",
        "orderId": "1715984730252115968",
        "projectId": "1715984730185007104",
        "state": 1,
        "updatedAt": 1753445347000
      },
      {
        "contactMobile": "17796371752",
        "contactName": "温尚儒2",
        "createdAt": 1753445337000,
        "createdUser": "1701776612231847936",
        "createdUserName": "温尚儒",
        "createdUserPhone": "17796371751",
        "finishTime": 1753445678000,
        "flow": 1,
        "id": "1715984688665591809",
        "orderId": "1715984688661397504",
        "projectId": "1715984688447488000",
        "relateId": "1715986118969393152",
        "reservationTime": 1753923600000,
        "state": 1,
        "updatedAt": 1753445678000
      }
    ],
    "empty": false,
    "first": true,
    "last": false,
    "number": 0,
    "numberOfElements": 10,
    "pageable": {
      "offset": 0,
      "pageNumber": 0,
      "pageSize": 10,
      "paged": true,
      "sort": {
        "empty": true,
        "sorted": false,
        "unsorted": true
      },
      "unpaged": false
    },
    "size": 10,
    "sort": {
      "empty": true,
      "sorted": false,
      "unsorted": true
    },
    "totalElements": 637,
    "totalPages": 64
  },
  "errcode": "0000",
  "errmsg": "成功"
}
```
