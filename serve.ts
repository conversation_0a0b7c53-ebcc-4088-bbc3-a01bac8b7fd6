import { spawnSync } from 'node:child_process'
import fs from 'node:fs'
import path, { dirname } from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = dirname(fileURLToPath(import.meta.url))

function loadEnvFile() {
  const envFile = path.resolve(__dirname, '.env.development.local')
  const content = `
      # 在测试服或正式服登录后，在控制中通过 'localStorage.sc_refresh_token' 获取 value 的值|
      VITE_APP_REFRESH_TOKEN=|
    `.replace(/\n\s+/g, '').replace(/\|/g, '\n')

  // 检测是否有 .env.development.local 文件, 如果不存在，则创建一个
  if (!fs.existsSync(envFile)) {
    fs.writeFileSync(envFile, content)
  }
  else {
    // 如果存在，则读取文件内容
    const envContent = fs.readFileSync(envFile, 'utf-8')
    // 如果不存在插入 VITE_APP_REFRESH_TOKEN=
    if (!envContent.includes('VITE_APP_REFRESH_TOKEN=')) {
      fs.appendFileSync(envFile, content)
    }
  }
}

(async () => {
  loadEnvFile()

  // 启动 vite
  spawnSync('npx', ['vite', '--force'], { stdio: 'inherit' })
})()
