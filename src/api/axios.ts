import { ServiceToken } from '@admin/service'
import { APP_API_BASEURL, APP_API_PROXY_PREFIX, APP_SCID, IsDev, OPEN_PROXY, UserInfo } from '@admin/utils'
import { http as axios } from '@shencom/request'
import router from '@/router'
import { getCacheOrganizationId } from '@/store/modules/organization'

function Token() {
  const Authorization = UserInfo.getToken()
  return Authorization ? `bearer ${Authorization}` : null
}

axios.defaults.headers.common.timeout = 20000

axios.defaults.headers.common.scid = APP_SCID

axios.defaults.baseURL = (IsDev && OPEN_PROXY) ? APP_API_PROXY_PREFIX : APP_API_BASEURL

axios.interceptors.request.use(
  (res) => {
    const { Authorization } = res.headers!
    if (Authorization === null || JSON.stringify(Authorization) === '{}') {
      delete res.headers?.Authorization
    }
    else {
      const token = Token()
      if (token) {
        res.headers!.Authorization = token
      }
      else {
        delete res.headers?.Authorization
      }
    }

    const organizationId = getCacheOrganizationId()
    if (organizationId) {
      res.headers!.organizationId = organizationId
    }

    // 业务处理
    console.log('%c 请求链接 :>', 'font-size:13px;font-weight:bold;color:#409eff;', res.url)
    console.log('%c 请求数据 :>', 'font-size:13px;font-weight:bold;color:#409eff;', res)

    return res
  },
  (error) => {
    console.log('%c 错误请求 :>', 'font-size:13px;font-weight:bold;color:#f56c6c;', error)

    // 业务处理
    return Promise.reject(error)
  },
)

axios.interceptors.response.use(
  (res) => {
    // 业务处理
    console.log(`%c 响应链接 :>`, 'font-size:13px;font-weight:bold;color:#67c23a;', res.config.url)
    console.log('%c 响应数据 :>', 'font-size:13px;font-weight:bold;color:#67c23a;', res)
    return res
  },
  async (error) => {
    console.log('%c 错误响应 :>', 'font-size:13px;font-weight:bold;color:#f56c6c;', error)

    const { status } = error.response || {}

    if (status === 401 && error.config?.url) {
      const { url } = error.config
      if (url && !url.includes('/auth/token-user/refresh')) {
        const isLogin = await ServiceToken.login()
        if (isLogin) {
          return axios.request(error.config)
        }
      }

      UserInfo.clearAllUser()
      setTimeout(() => {
        router.replace('/login')
      }, 800)
    }

    // 业务处理
    return Promise.reject(error)
  },
)

export { axios }
