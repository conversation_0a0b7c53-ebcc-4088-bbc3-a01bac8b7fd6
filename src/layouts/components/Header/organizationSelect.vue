<script setup lang="ts">
import { ServiceSite, ServiceUser } from '@admin/service'

defineOptions({
  name: 'OrganizationSelect',
})

const organizationStore = useOrganizationStore()

const organization = computed(() => organizationStore.organization)

async function refreshPermission() {
  ServiceUser.removeCacheMenus()
  ServiceUser.removeCachePermissions()
  ServiceUser.removeCacheOrganization()
  ServiceSite.removeCache()
  window.location.reload()
}

function onDropdownCommand(command: string) {
  if (command !== organizationStore.currentOrganization?.id) {
    organizationStore.setCacheOrganizationId(command)
    refreshPermission()
  }
}

const items = ref([
  organization.value.map(item => ({
    label: item.name,
    icon: 'i-ep:office-building',
    disabled: item.id === organizationStore.currentOrganization?.id,
    handle: () => onDropdownCommand(item.id),
  })),
])
</script>

<template>
  <div class="w-full whitespace-nowrap">
    <FaDropdown v-if="organizationStore.currentOrganization" :items="items" @command="onDropdownCommand">
      <div class="flex items-center text-sm text-foreground/70">
        <FaIcon name="i-ep:office-building" class="mr-2" />
        <span class="mr-2">{{ organizationStore.currentOrganization?.name }}</span>
        <FaIcon name="i-ep:arrow-down" />
      </div>
    </FaDropdown>
  </div>
</template>
