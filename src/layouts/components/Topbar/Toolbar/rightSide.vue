<script setup lang="ts">
// import { OldPlatformService } from '@admin/service'
import { useSlots } from '@/slots'
import Tools from './tools.vue'

defineOptions({
  name: 'ToolbarRightSide',
})

// function onNavigateOldPlatform() {
//   OldPlatformService.navigateOldPlatform()
// }
</script>

<template>
  <div class="flex items-center">
    <!-- <div class="mr-2 cursor-pointer text-sm text-gray-500 underline" @click="onNavigateOldPlatform">
      使用旧版
    </div> -->
    <Tools mode="right-side" />
    <component :is="useSlots('toolbar-end')" />
  </div>
</template>
