<script setup lang="ts">
interface IProps {
  modelValue?: boolean
}
interface IEmits {
  (e: 'update:modelValue', val: any): void
}
const props = defineProps<IProps>()

const emits = defineEmits<IEmits>()

const footer: string[] = []
// const footer = ['版权声明', '法律什么', '联系我们', '网站帮助', '常见问题']
</script>

<template>
  <div class="flex-jc fixed bottom-0 left-0 right-0 h-25">
    <el-space :size="10" spacer="|" class="text-white">
      <el-button v-for="item in footer" :key="item" type="text">
        <span class="text-white">
          {{ item }}
        </span>
      </el-button>
    </el-space>
  </div>
</template>
