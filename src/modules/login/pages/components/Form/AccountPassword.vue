<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { ServiceSite } from '@admin/service'
import { ExceptionHandler, ExceptionToast } from '@admin/utils'
import { ApiResetPwd } from '@shencom/api'
import { ElLoading } from 'element-plus'
import { toast } from 'vue-sonner'
import MailComponent from './MailComponent.vue'

defineOptions({ name: 'AccountPassword' })

function formDefault() {
  return {
    username: '',
    password: '',
    checkPass: '',
    mailCode: '',
  } as const
}

const modelValue = defineModel<boolean>({ required: true })

const form = ref(formDefault())
const formRef = ref<FormInstance>()

const rules = ref<FormInstance['rules']>({
  username: [{ required: true, message: '请输入手机号或用户名', trigger: 'blur' }],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    {
      required: true,
      pattern: ServiceSite.passwordConfig.pattern,
      message: ServiceSite.passwordConfig.message,
      trigger: 'blur',
    },
  ],
  checkPass: [
    { required: true, message: '请再次输入密码', trigger: 'blur' },
    {
      validator: (_, val, callback) => {
        if (val !== form.value.password) {
          callback(new Error('两次输入密码不一致!'))
        }
        else {
          callback()
        }
      },
      trigger: 'blur',
    },
  ],
  mailCode:
    [
      { required: true, message: '请输入验证码' },
      { min: 6, max: 6, message: '验证码长度有误' },
    ],
})

function resetForm() {
  form.value = formDefault()
}

async function validateForm() {
  return new Promise((resolve) => {
    formRef.value?.validate((valid) => {
      resolve(valid)
    })
  })
}

async function submit() {
  const valid = await validateForm()

  if (!valid) {
    return
  }

  const loading = ElLoading.service({ text: '加载中...' })
  try {
    const params = {
      param: form.value.username,
      code: form.value.mailCode,
      password: form.value.password,
    }
    await ApiResetPwd(params)

    toast.success('修改成功')
    modelValue.value = false
  }
  catch (error) {
    ExceptionToast(error, '修改失败')
    ExceptionHandler(error)
  }
  finally {
    loading.close()
  }
}
</script>

<template>
  <el-dialog v-model="modelValue" title="修改密码" append-to-body :z-index="999">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" status-icon size="large">
      <el-form-item label="手机号或用户名" prop="username">
        <el-input v-model.trim="form.username" :maxlength="11" clearable autofocus placeholder="填写你需要找回登录密码的手机号或用户名">
          <template #prefix>
            <el-icon size="20">
              <div class="i-ep:user h-1em w-1em" />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input v-model.trim="form.password" placeholder="请输入密码" clearable show-password type="password">
          <template #prefix>
            <el-icon size="20">
              <div class="i-ep:lock h-1em w-1em" />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="确认密码" prop="checkPass">
        <el-input v-model.trim="form.checkPass" placeholder="请再次输入密码" clearable show-password type="password">
          <template #prefix>
            <el-icon size="20">
              <div class="i-ep:lock h-1em w-1em" />
            </el-icon>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="验证码" prop="mailCode">
        <MailComponent v-model="form.mailCode" :username="form.username" type="all" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="large" @click="resetForm">
        重置
      </el-button>
      <el-button size="large" @click="modelValue = false">
        取消
      </el-button>
      <el-button size="large" @click="submit">
        提交
      </el-button>
    </template>
  </el-dialog>
</template>
