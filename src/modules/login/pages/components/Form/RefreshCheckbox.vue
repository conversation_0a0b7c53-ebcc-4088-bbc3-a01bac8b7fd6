<script setup lang="ts">
import { ServiceToken } from '@admin/service'
import { IsPro } from '@admin/utils'

// 是否开启30天免登录
const enable = ref<boolean>(ServiceToken.enable)

const defaultValue = ref(ServiceToken.get30DayLogin() ?? !IsPro) // 正式服默认不勾选

watch(defaultValue, (val) => {
  ServiceToken.set30DayLogin(Boolean(val))
})
</script>

<template>
  <el-checkbox v-if="enable" v-model="defaultValue">
    <span class="mr-2 text-foreground">30天内自动登录</span>
  </el-checkbox>
</template>
