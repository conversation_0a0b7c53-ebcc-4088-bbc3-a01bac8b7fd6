<script setup lang="ts">
import type { FormInstance } from 'element-plus'
import { ExceptionToast, Storages } from '@admin/utils'
import { ApiWechatBindPhone } from '@shencom/api'
import { ElLoading } from 'element-plus'
import { toast } from 'vue-sonner'
import MailComponent from './MailComponent.vue'

defineOptions({ name: 'WeixinBind' })

const emits = defineEmits<IEmits>()

interface IEmits {
  (e: 'success', data: SC.User.RootInfo): void
}

const modelValue = defineModel<boolean>({ required: true })

const form = ref({
  username: '',
  mailCode: '',
})
const formRef = ref<FormInstance>()

const rules = ref<FormInstance['rules']>({
  username: [{ required: true, message: '请输入手机号或用户名', trigger: 'blur' }],
  mailCode:
    [
      { required: true, message: '请输入验证码' },
      { min: 6, max: 6, message: '验证码长度有误' },
    ],
})

async function validateForm() {
  return new Promise((resolve) => {
    formRef.value?.validate((valid) => {
      resolve(valid)
    })
  })
}

async function onSubmit() {
  const valid = await validateForm()

  if (!valid) {
    return
  }

  const openId = Storages.get<string>('weixin_openId')

  if (!openId) {
    toast.error('扫码过期，请重新扫码')
    return
  }

  const loading = ElLoading.service({ target: formRef.value?.$el, text: '加载中...' })
  try {
    const body = { code: form.value.mailCode, phone: form.value.username, openid: openId }
    const { data } = await ApiWechatBindPhone(body)

    toast.success('绑定成功，正在登录...')

    setTimeout(() => {
      emits('success', data)
      modelValue.value = false
    }, 1000)
  }
  catch (error) {
    ExceptionToast(error, '绑定失败')
  }
  finally {
    loading.close()
  }
}
</script>

<template>
  <el-form ref="formRef" class="m-auto p-6" :model="form" :rules="rules" label-width="auto" status-icon size="large">
    <el-form-item prop="username">
      <el-input v-model.trim="form.username" :maxlength="11" clearable autofocus placeholder="手机号或用户名">
        <template #prefix>
          <el-icon size="20">
            <div class="i-ep:user h-1em w-1em" />
          </el-icon>
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="mailCode">
      <MailComponent v-model="form.mailCode" :username="form.username" type="all" @submit="onSubmit" />
    </el-form-item>
    <el-form-item>
      <el-button class="w-full" size="large" type="primary" @click="onSubmit">
        绑定
      </el-button>
    </el-form-item>
    <el-form-item>
      <el-button link class="w-full" size="large" type="primary" @click="modelValue = false">
        重新扫码
      </el-button>
    </el-form-item>
  </el-form>
</template>
