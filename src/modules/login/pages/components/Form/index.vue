<script setup lang="ts">
import { ServiceSite } from '@admin/service'
import { ExceptionHandler, ExceptionToast, Storages, UserInfo } from '@admin/utils'
import { ApiWechatCodeLogin } from '@shencom/api'
import { UrlParam } from '@shencom/utils'
import { useToggle } from '@vueuse/core'
import { ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'
import { toast } from 'vue-sonner'
import { setUrlHref } from '../../../utils'
import Account from './Account.vue'
import Weixin from './Weixin.vue'

const router = useRouter()
const userStore = useUserStore()

const [weixinLogin, toggleWeixinLogin] = useToggle(ServiceSite.config('login_method') === '2')

const weixinRef = ref<InstanceType<typeof Weixin>>()

const weixinLoginEnable = computed(() => ServiceSite.config('scan_login_active') === '1')

onMounted(async () => {
  const code = UrlParam('code')
  if (code) {
    Storages.set('weixin_code', code, 5)
    onWeixinLogin(code)
  }
})

async function onWeixinLogin(code: string) {
  setUrlHref()

  const onClose = ElLoading.service({ text: '登录中...' })
  try {
    const { data } = await ApiWechatCodeLogin(code)
    if (typeof data === 'string') {
      toast.warning('该微信未绑定系统用户，请先绑定！')

      Storages.set('weixin_openId', data, 10)
      await nextTick()
      weixinRef.value?.onOpenWeixinBind()
    }
    else {
      toast.success('登录成功!')
      onSuccess(data.oAuth2AccessToken, 'weixin')
    }

    Storages.remove('weixin_code')
  }
  catch (error) {
    ExceptionHandler(error)
    ExceptionToast(error, '登录失败')

    const code = Storages.get('weixin_code')
    if (code) {
      Storages.remove('weixin_code')
      onWeixinLogin(code)
    }
  }
  finally {
    onClose.close()
  }
}

function onSuccess(data: SC.User.RootInfo, loginType?: string) {
  UserInfo.setRootInfo(data)

  // TODO: 首次登录是否需要修改密码
  // const hasNeedChangePwd = await this.handleJudgeNeedChangePwd(loginType);
  //   if (hasNeedChangePwd) {
  //    Storage.set('temporaryUserInfo', userInfo);
  //     return;
  //   }

  userStore.login()
  router.replace('/')
}
</script>

<template>
  <div class="login-form flex-c relative size-full rounded-3">
    <template v-if="weixinLoginEnable">
      <span
        class="qrcode absolute right-0 top-0 z-10 h-12 w-12 cursor-pointer text-foreground"
        @click="toggleWeixinLogin()"
      >
        <i v-show="!weixinLogin" class="i-ant-design-qrcode-outlined size-full text-12" />
        <i v-show="weixinLogin" class="i-ant-design:laptop-outlined size-full text-12" />
      </span>
    </template>

    <div v-if="weixinLoginEnable" v-show="weixinLogin" class="size-full">
      <Weixin ref="weixinRef" v-model="weixinLogin" @success="onSuccess" />
    </div>
    <div v-show="!weixinLogin" class="size-full">
      <Account @success="onSuccess" />
    </div>
  </div>
</template>

<style scoped>
.qrcode {
  clip-path: polygon(0 10%, 100% 0, 90% 100%);
}
</style>
