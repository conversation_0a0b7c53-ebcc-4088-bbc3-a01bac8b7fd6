<script setup lang="ts">
import { ServiceSite } from '@admin/service'
import { useStorage, useToggle } from '@vueuse/core'
import Banner from './components/Banner.vue'
import Footer from './components/Footer.vue'
import Form from './components/Form/index.vue'

defineOptions({ name: 'LoginPage' })

const carousel = ref([
  new URL('../assets/images/<EMAIL>', import.meta.url).href,
])

const logo = computed(() => ServiceSite.logo)

const loginMode = useStorage('loginMode', 'fantastic')
const loginModeChange = useToggle(loginMode, {
  falsyValue: 'fantastic',
  truthyValue: 'default',
})
</script>

<template>
  <div class="login-container">
    <div v-if="loginMode === 'default'" class="h-full w-full flex">
      <div class="flex-c flex-1">
        <div class="rounded-lg bg-[#324356]/20 p-5 shadow-xl">
          <div class="h-120 w-90 flex-col">
            <Form class="flex-1" />
            <!-- <div class="mt-5 flex-jc text-white font-bold hover:text-blue" @click="loginModeChange()">
              切换页面风格
            </div> -->
          </div>
        </div>
      </div>
      <div class="flex-c flex-1">
        <Banner :carousel="carousel" class="w-600px" />
      </div>
    </div>

    <div v-else-if="loginMode === 'fantastic'" class="login-box rounded-lg bg-[#324356]/20">
      <div class="banner relative w-120 bg-black/10">
        <img :src="logo" class="absolute left-4 top-4 h-10 rounded-1 opacity-70">
        <div class="h-full flex-c">
          <Banner :carousel="carousel" class="w-full" />
        </div>
      </div>
      <div class="p-5">
        <div class="h-120 w-90 flex-col">
          <Form class="flex-1" />
          <!-- <div class="mt-5 flex-jc text-white font-bold hover:text-blue" @click="loginModeChange()">
            切换页面风格
          </div> -->
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<style lang="scss" scoped>
.login-container {
  width: 100vw;
  height: 100vh;
  background-image: url("../assets/images/img_bg.png");
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
}

.login-box {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  box-shadow: var(--el-box-shadow);
  transform: translateX(-50%) translateY(-50%);
}

// TODO: 移动端未适配完成
[data-mode="mobile"] {
  .login-box {
    flex-direction: column;
  }

  .banner {
    width: 100%;
    // height: 300px;
  }
}
</style>
