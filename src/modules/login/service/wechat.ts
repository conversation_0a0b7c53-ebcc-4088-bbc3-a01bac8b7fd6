import { ApiWechatGetQrcode } from '@shencom/api'

/**
  @charset "UTF-8";
  body{background: var(--background,transparent);}
  .impowerBox .qrcode {width: 180px;}
  .impowerBox #wx_default_tip p:nth-child(2) {display: none;}
  .impowerBox .title {display: none;}
  .impowerBox #wx_default_tip p:nth-child(1),.info .status_txt,
  .impowerBox .status,
  .impowerBox .status_txt {color:var(--weui-FG-0,#fff);}
  @media (prefers-color-scheme: dark) {
    body {--background: rgba(20, 20, 20);}
  }
 */
const href = 'DQpAY2hhcnNldCAiVVRGLTgiOw0KYm9keXtiYWNrZ3JvdW5kOiB2YXIoLS1iYWNrZ3JvdW5kLHRyYW5zcGFyZW50KTt9DQouaW1wb3dlckJveCAucXJjb2RlIHt3aWR0aDogMTgwcHg7fQ0KLmltcG93ZXJCb3ggI3d4X2RlZmF1bHRfdGlwIHA6bnRoLWNoaWxkKDIpIHtkaXNwbGF5OiBub25lO30NCi5pbXBvd2VyQm94IC50aXRsZSB7ZGlzcGxheTogbm9uZTt9DQouaW1wb3dlckJveCAjd3hfZGVmYXVsdF90aXAgcDpudGgtY2hpbGQoMSksLmluZm8gLnN0YXR1c190eHQsDQouaW1wb3dlckJveCAuc3RhdHVzLA0KLmltcG93ZXJCb3ggLnN0YXR1c190eHQge2NvbG9yOnZhcigtLXdldWktRkctMCwjZmZmKTt9DQpAbWVkaWEgKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKSB7DQogIGJvZHkgey0tYmFja2dyb3VuZDogcmdiYSgyMCwgMjAsIDIwKTt9DQp9'

export async function ServiceWechatGetQrcode() {
  const { data } = await ApiWechatGetQrcode({
    redirect: `${window.location.origin}/index.html`,
  })
  const url = `${data}&href=${href}`

  return url
}
