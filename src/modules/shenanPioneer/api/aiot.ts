/**
 * AIOT 相关 API
 */

import type { WarningEventItem, WarningEventParams } from './types'
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 获取实时警告事件列表
 * @param params 请求参数
 * @returns 实时警告事件响应数据
 */
export async function fetchWarningEventList(params?: WarningEventParams) {
  const requestParams = {
    size: 10,
    page: 0,
    ...params,
  }

  return http.post<SC.API.IndexInterface<WarningEventItem>>(`${url}/aiot/event/index`, requestParams)
}
