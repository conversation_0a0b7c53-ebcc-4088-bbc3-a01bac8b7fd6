/**
 * 流程监控相关 API
 */

import type { FlowItem, FlowParams } from './types'
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 获取流程监控列表
 * @param params 请求参数
 * @returns 流程监控响应数据
 */
export async function fetchFlowList(params?: FlowParams) {
  const requestParams = {
    size: 10,
    page: 0,
    ...params,
  }

  return http.post<SC.API.IndexInterface<FlowItem>>(`${url}/monitor/flow/index`, requestParams)
}

/**
 * 获取待预约安装列表
 * @param params 请求参数
 * @returns 待预约安装数据
 */
export async function fetchPendingInstallationReservation(params?: Omit<FlowParams, 'flow'>) {
  const requestParams: FlowParams = {
    size: 5,
    page: 0,
    ...params,
    flow: '0,1', // 工程创建,安装预约
  }

  return fetchFlowList(requestParams)
}

/**
 * 获取待预约回收列表
 * @param params 请求参数
 * @returns 待预约回收数据
 */
export async function fetchPendingRecyclingReservation(params?: Omit<FlowParams, 'flow'>) {
  const requestParams: FlowParams = {
    size: 5,
    page: 0,
    ...params,
    flow: '4,5', // 接入监管,施工完成
  }

  return fetchFlowList(requestParams)
}

/**
 * 获取待勘查列表
 * @param params 请求参数
 * @returns 待勘查数据
 */
export async function fetchPendingInspection(params?: Omit<FlowParams, 'flow' | 'status'>) {
  const requestParams: FlowParams = {
    size: 5,
    page: 0,
    ...params,
    status: 0, // 未完成
    flow: '2', // 现场勘察
  }

  return fetchFlowList(requestParams)
}

/**
 * 获取待安装列表
 * @param params 请求参数
 * @returns 待安装数据
 */
export async function fetchPendingInstallation(params?: Omit<FlowParams, 'flow' | 'status'>) {
  const requestParams: FlowParams = {
    size: 5,
    page: 0,
    ...params,
    status: 0, // 未完成
    flow: '3', // 上门安装
  }

  return fetchFlowList(requestParams)
}

/**
 * 获取待回收列表
 * @param params 请求参数
 * @returns 待回收数据
 */
export async function fetchPendingRecycling(params?: Omit<FlowParams, 'flow' | 'status'>) {
  const requestParams: FlowParams = {
    size: 5,
    page: 0,
    ...params,
    status: 0, // 未完成
    flow: '7', // 上门回收
  }

  return fetchFlowList(requestParams)
}
