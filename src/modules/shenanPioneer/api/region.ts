import { http } from '@shencom/request'
import { url } from './config'

export interface RegionTreeBody {
  /**
   * 深度
   * 不传root 或 root 为 false 时
   * deep 为 0  不返回任何数据
   * deep 为 1  返回当前根节点下一级的数据 以此类推
   * root为 true 时
   * deep 为 0  只返回当前根节点数据
   * deep 为 1  返回当前根节点下一级的数据 以此类推
   */
  deep?: number
  /**
   * 根节点id
   * 默认为2 返回深圳市的区数据 不包括深圳市
   * 3  光明区、79 福田区、80 罗湖区、81 南山区、82 盐田区
   * 83 宝安区、84 龙华区、85 龙岗区、86 坪山区、87 大鹏新区
   */
  pId?: string
  /**
   * 是否返回顶节点 true是 false否 默认返回
   */
  root?: boolean

}

export interface RegionTree {
  id: string
  title: string
  pId: string
  weight: number
  children?: RegionTree[]

  [x: string]: any
}

/**
 * 获取用户区域树 `有权限限制`
 * @description 获取用户区域树
 */
export async function fetchRegionTree(body?: RegionTreeBody) {
  const res = await http.post<RegionTree[]>(`${url}/com/region/tree`, body)
  return res.data
}

/**
 * 获取用户区域树 `有权限限制`
 * @description 获取用户区域树
 */
export async function fetchUserRegionTree(body?: RegionTreeBody) {
  const res = await http.post<RegionTree[]>(`${url}/com/region/role/tree`, body)
  return res.data
}
