import type { ProjectStatistics, ViolationTrendItem, ViolationTrendParams } from './types'
import { http } from '@shencom/request'
import { url } from './config'
/**
 * 获取项目统计数据
 * @returns Promise<ProjectStatistics>
 */
export function fetchProjectStatistics() {
  return http.post<ProjectStatistics>(`${url}/report/project/statistics`)
}

/**
 * 获取违规告警趋势数据
 * @param params 请求参数
 * @returns 违规告警趋势数据
 */
export function fetchViolationTrend(params?: ViolationTrendParams) {
  return http.post<ViolationTrendItem[]>(`${url}/aiot/event/statistics/violation-trend`, params)
}
