/**
 * sporadic 相关 API
 */

import type { ProjectRegionItem, ProjectRegionParams } from './types'
import { http } from '@shencom/request'
import { url } from './config'

/**
 * 获取工程区域分布统计
 * @param params - 请求参数
 * @returns 工程区域分布数据
 */
export function fetchProjectRegionStatistics(params: ProjectRegionParams) {
  return http.post<ProjectRegionItem[]>(`${url}/sporadic/project/region/statistics`, params)
}
