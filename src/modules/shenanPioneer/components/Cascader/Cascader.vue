<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import { defu } from 'defu'
import { ElCascader } from 'element-plus'

interface Props {
  /** 绑定值 */
  modelValue?: string | number | Array<string | number>
  /** 是否禁用 */
  disabled?: boolean
  /** 是否可清空 */
  clearable?: boolean
  /** 是否可搜索 */
  filterable?: boolean
  /** 输入框占位文本 */
  placeholder?: string
  /** 是否多选 */
  multiple?: boolean
  /** 自定义数据源 */
  options?: Record<string, any>[]
  /** 自定义字段映射 */
  props?: {
    value?: string
    label?: string
    children?: string
    disabled?: string
    /** 是否仅叶子节点可选 */
    checkStrictly?: boolean
    /** 展开触发方式 */
    expandTrigger?: 'click' | 'hover'
  }
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: undefined,
  disabled: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择',
  multiple: false,
  props: () => ({
    value: 'id',
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    checkStrictly: false,
    expandTrigger: 'hover',
  }),
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: CascaderValue): void
  (e: 'expandChange', value: CascaderValue): void
}>()

const defaultProps: Props = {
  disabled: false,
  clearable: true,
  filterable: true,
  placeholder: '请选择',
  multiple: false,
  props: {
    value: 'id',
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    checkStrictly: false,
    expandTrigger: 'hover',
  },
}

// 内部值
const value = ref(props.modelValue)

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  value.value = newVal
})

// // 监听内部值变化
// watch(value, (newVal) => {
//   emit('update:modelValue', newVal)
// })

// 获取值变更
function handleChange(val: any) {
  emit('update:modelValue', val)
  emit('change', val)
}

// 获取展开变更
function handleExpandChange(val: CascaderValue) {
  emit('expandChange', val)
}

const attrs = useAttrs()

const cascaderProps = computed(() => {
  return defu(attrs, props, defaultProps) as Props
})
</script>

<template>
  <ElCascader
    v-model="value" v-bind="cascaderProps" class="w-full" @change="handleChange"
    @expand-change="handleExpandChange"
  />
</template>
