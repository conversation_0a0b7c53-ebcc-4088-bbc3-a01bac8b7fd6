# RegionCascader 区域选择组件

## 组件概述

`RegionCascader` 是一个基于 Element Plus 的级联选择器封装的区域选择组件，用于选择行政区域。该组件自动从API获取区域数据并进行本地缓存，提高性能和用户体验。

## 特性

- 自动加载区域数据
- 本地缓存（localStorage）
- 支持V-model双向绑定
- 内置数据刷新按钮
- 完整继承Element Plus Cascader的功能
- 可配置是否包含顶级节点（root）

## 属性 (Props)

| 属性名        | 类型          | 默认值 | 说明                             |
| ------------- | ------------- | ------ | -------------------------------- |
| modelValue    | CascaderValue | -      | 选中项绑定值                     |
| root        | boolean       | false  | 是否显示顶级节点                 |
| checkStrictly | boolean       | false  | 是否严格的遵守父子节点不互相关联 |

### root 属性说明

`root` 属性用于控制是否显示顶级节点：

- 当 `root = false`（默认值）：组件只显示第一个节点的子节点，即不包含顶级节点（例如：只显示深圳市下的区域，不包含深圳市本身）
- 当 `root = true`：组件显示完整的节点树，包含顶级节点

## 默认配置

组件集成了以下默认配置，可通过传递相应的props进行覆盖：

```ts
const defaultProps = {
  clearable: true,
  filterable: true,
  placeholder: '请选择',
  props: {
    value: 'id',
    label: 'title',
    expandTrigger: 'hover',
  },
}
```

### emitPath 属性说明

`props.emitPath` 控制选中值的返回格式：

- 当 `emitPath = true`（默认）：返回一个包含所有选中节点ID的数组，例如：`['2', '3', '4']`
- 当 `emitPath = false`：仅返回最后一级选中节点的ID，例如：`'4'`

## 事件 (Events)

| 事件名            | 参数          | 说明                                    |
| ----------------- | ------------- | --------------------------------------- |
| update:modelValue | CascaderValue | 在选中值变化时触发，用于v-model双向绑定 |
| change            | CascaderValue | 在选中节点变化时触发                    |

## 功能和插槽

- **数据加载**：组件首次打开时会自动加载区域数据
- **数据缓存**：区域数据会被缓存在localStorage中，避免频繁请求API
- **刷新按钮**：内置刷新按钮，可清除缓存并重新加载数据
- **空数据处理**：内置空数据提示，包括加载中、加载失败等状态

## 使用示例

### 基本用法

```vue
<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import { RegionCascader } from '@drone/components/RegionCascader'
import { ref } from 'vue'

const selectedRegion = ref<CascaderValue>([])

function handleRegionChange(value: string | string[]) {
  console.log('选中的区域:', value)
}
</script>

<template>
  <RegionCascader
    v-model="selectedRegion"
    placeholder="请选择区域"
    @change="handleRegionChange"
  />
</template>
```

### 多选模式示例

```vue
<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import { RegionCascader } from '@drone/components/RegionCascader'
import { ref } from 'vue'

const selectedRegions = ref<CascaderValue>([])
</script>

<template>
  <RegionCascader
    v-model="selectedRegions"
    placeholder="请选择多个区域"
    :props="{ multiple: true }"
  />
</template>
```

### emitPath 属性示例

```vue
<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import { RegionCascader } from '@drone/components/RegionCascader'
import { ref } from 'vue'

// emitPath=true时（默认）：返回路径数组
const selectedRegionWithPath = ref<string[]>([])

// emitPath=false时：仅返回最终选中的ID
const selectedRegionWithoutPath = ref<string>('')

function handleChange1(value: string[]) {
  console.log('包含路径的值:', value) // 例如：['2', '79', '101']
}

function handleChange2(value: string) {
  console.log('不包含路径的值:', value) // 例如：'101'
}
</script>

<template>
  <div class="space-y-4">
    <!-- 默认：返回完整路径 -->
    <div>
      <p>默认：emitPath=true</p>
      <RegionCascader
        v-model="selectedRegionWithPath"
        placeholder="返回完整路径"
        @change="handleChange1"
      />
    </div>

    <!-- 设置emitPath=false：仅返回选中节点ID -->
    <div>
      <p>emitPath=false</p>
      <RegionCascader
        v-model="selectedRegionWithoutPath"
        placeholder="仅返回选中节点ID"
        :props="{ emitPath: false }"
        @change="handleChange2"
      />
    </div>
  </div>
</template>
```

### 显示顶级节点示例

```vue
<script setup lang="ts">
import type { CascaderValue } from 'element-plus'
import { RegionCascader } from '@drone/components/RegionCascader'
import { ref } from 'vue'

const selectedRegion = ref<CascaderValue>([])
</script>

<template>
  <RegionCascader
    v-model="selectedRegion"
    placeholder="显示顶级节点"
    :root="true"
  />
</template>
```
