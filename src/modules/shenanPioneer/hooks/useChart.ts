import type { Chart } from '@antv/g2'

export function useChart(chart: Chart | null) {
  const settingsStore = useSettingsStore()

  const chartContainer = ref<HTMLElement>()

  const isDarkTheme = computed(() => settingsStore.currentColorScheme === 'dark')

  const theme = computed(() => isDarkTheme.value ? 'dark' : 'light')

  watch(theme, updateChartTheme)

  /**
   * 更新图表数据
   */
  function updateChart(data: any) {
    if (!chart) {
      return
    }
    chart.changeData(data)
  }

  function updateChartTheme() {
    if (!chart) {
      return
    }

    chart.theme({ type: theme.value })
    chart.render()
  }

  // 监听窗口大小变化
  let resizeObserver: ResizeObserver | null = null

  /**
   * 重新渲染图表
   */
  function resizeChart() {
    if (chart) {
      chart.forceFit()
    }
  }

  /**
   * 销毁图表
   */
  function destroyChart() {
    if (chart) {
      chart.destroy()
      chart = null as any
    }
  }

  onMounted(async () => {
    // 设置窗口大小监听
    if (chartContainer.value && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        resizeChart()
      })
      resizeObserver.observe(chartContainer.value)
    }
  })

  onUnmounted(() => {
    destroyChart()
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
  })

  function setChart(_chart: Chart) {
    chart = _chart
  }

  return {
    theme,
    chartContainer,
    chart,
    setChart,
    updateChart,
  }
}
