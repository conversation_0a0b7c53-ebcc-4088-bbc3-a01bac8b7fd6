import { Dayjs, FormatDateTime } from '@shencom/utils'

/**
 * 获取日期范围
 * @param filter 过滤条件
 * @returns 日期范围 [开始时间戳, 结束时间戳] 或 undefined
 */
export function getDateRange(filter: '今日' | '昨日' | '全部' | (string & {})): string {
  const now = Dayjs()

  switch (filter) {
    case '今日': {
      const startOfDay = FormatDateTime(now.startOf('day'))
      const endOfDay = FormatDateTime(now.endOf('day'))
      return `${startOfDay},${endOfDay}`
    }
    case '昨日': {
      const yesterday = now.subtract(1, 'day')
      const startOfDay = FormatDateTime(yesterday.startOf('day'))
      const endOfDay = FormatDateTime(yesterday.endOf('day'))
      return `${startOfDay},${endOfDay}`
    }
    case '全部':
    default:
      return ''
  }
}
