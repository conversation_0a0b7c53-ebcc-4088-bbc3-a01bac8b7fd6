import type { TableColumnCtx, TagProps } from 'element-plus'
import { FormatDateTime } from '@shencom/utils'

interface Option {
  value: number | string
  label: string
}

interface TypeOption extends Option {
  type?: TagProps['type']
  [key: string]: any
}

export function formatterDateTime<T extends Record<string, any>>(row: T, col: TableColumnCtx<T>) {
  return row[col.property as keyof T] ? FormatDateTime(row[col.property as keyof T]) : '-'
}

export function formatterOptions<T extends Record<string, any>>(list: T[], map?: { label: string, value: string }): (T & { label: string, value: string })[] {
  const { label = 'label', value = 'value' } = map || {}
  return list.map(item => ({ ...item, label: item[label], value: item[value] }))
}

export function formatterTagType(value: string | number | undefined, options: TypeOption[]): TagProps['type'] {
  if (value === undefined || value === null) {
    return 'info'
  }
  const statusItem = options.find(item => item.value === value)
  return statusItem?.type || 'info'
}

export function formatterTagLabel(value: string | number | undefined, options: Option[]): string {
  if (value === undefined || value === null) {
    return '-'
  }
  const statusItem = options.find(item => item.value === value)
  return statusItem?.label || '-'
}

export function formatterFilters(options: Record<string, any>[], map?: { label: string, value: string }): TableColumnCtx<any>['filters'] {
  const { label = 'label', value = 'value' } = map || {}
  return options.map(item => ({ text: item[label], value: String(item[value]) }))
}
