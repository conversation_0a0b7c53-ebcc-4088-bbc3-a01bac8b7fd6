<!--
  数据概览组件
  显示备案施工单位、工程总数、施工中工程、总工程额等关键数据
-->
<script setup lang="ts">
import { fetchProjectStatistics } from '@shenanPioneer/api'
import { IsUndefined } from '@shencom/utils'
import Card from './Card.vue'

/**
 * 概览数据项接口
 */
interface OverviewItem {
  /** 标签名称 */
  label: string
  /** 数值 */
  value: number
  /** 单位 */
  unit?: string
  isHide?: boolean
}

// ==================== 响应式数据 ====================
const overviewData = ref<OverviewItem[]>([])

const loading = ref(false)

// ==================== 方法定义 ====================
/**
 * 获取项目统计数据
 */
async function getProjectStatistics() {
  try {
    loading.value = true
    const res = await fetchProjectStatistics()

    const data = res.data
    overviewData.value = [
      { label: '备案施工单位', value: data.contractingUnitCount || 0, isHide: IsUndefined(data.contractingUnitCount) },
      { label: '工程总数', value: data.totalProjectCount || 0, isHide: IsUndefined(data.totalProjectCount) },
      { label: '施工中工程', value: data.ongoingProjectCount || 0, isHide: IsUndefined(data.ongoingProjectCount) },
      { label: '总工程额', value: (data.totalAmount || 0) / 10000, unit: '万', isHide: IsUndefined(data.totalAmount) },
      { label: '待整改工程', value: data.rectifyProjectCount || 0, isHide: IsUndefined(data.rectifyProjectCount) },
    ]
  }
  catch (error) {
    console.error('获取项目统计数据异常:', error)
  }
  finally {
    loading.value = false
  }
}

// ==================== 生命周期 ====================
onMounted(() => {
  getProjectStatistics()
})
</script>

<template>
  <Card title="数据概览">
    <div class="mt-4 flex gap-4">
      <template v-for="(item, index) in overviewData" :key="index">
        <div
          v-if="!item.isHide" hover="shadow-lg hover:-translate-y-0.5 bg-primary/10"
          class="flex flex-col flex-1 items-center rounded-lg bg-primary/5 p-4 transition-all duration-300"
          :class="{ 'opacity-50': loading }"
        >
          <div class="text-ui-primary mb-2 text-2xl font-bold">
            <span v-if="loading">0</span>
            <FaAnimatedCountTo
              :value="item.value" will-change :format="{
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              }"
            />
          </div>

          <div class="flex text-center text-sm text-foreground/80 font-medium">
            {{ item.label }}
            <span v-if="item.unit">({{ item.unit }})</span>
          </div>
        </div>
      </template>
    </div>
  </Card>
</template>
