<!--
  待服务清单组件
  显示待勘查、待安装、待回收的工程列表
-->
<script setup lang="ts">
import type { FlowItem } from '@shenanPioneer/api'
import { fetchPendingInspection, fetchPendingInstallation, fetchPendingRecycling } from '@shenanPioneer/api'
import { FormatDate } from '@shencom/utils'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import Card from './Card.vue'

// 路由实例 (暂时未使用，保留用于后续路由跳转功能)
const _router = useRouter()

// 响应式数据
const loading = ref(false)
const inspectionList = ref<FlowItem[]>([])
const installationList = ref<FlowItem[]>([])
const recyclingList = ref<FlowItem[]>([])
const inspectionTotal = ref(0)
const installationTotal = ref(0)
const recyclingTotal = ref(0)
const currentPage = ref(0)
const pageSize = ref(5) // 显示5条数据

/**
 * 获取待勘查数据
 */
async function fetchInspectionData() {
  loading.value = true
  try {
    const response = await fetchPendingInspection({
      size: pageSize.value,
      page: currentPage.value,
    })

    inspectionList.value = response.data.content
    inspectionTotal.value = response.data.totalElements
  }
  catch (error) {
    console.error('获取待勘查数据失败:', error)
    ElMessage.error('获取待勘查数据失败')
    inspectionList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取待安装数据
 */
async function fetchInstallationData() {
  loading.value = true
  try {
    const response = await fetchPendingInstallation({
      size: pageSize.value,
      page: currentPage.value,
    })

    installationList.value = response.data.content
    installationTotal.value = response.data.totalElements
  }
  catch (error) {
    console.error('获取待安装数据失败:', error)
    ElMessage.error('获取待安装数据失败')
    installationList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取待回收数据
 */
async function fetchRecyclingData() {
  loading.value = true
  try {
    const response = await fetchPendingRecycling({
      size: pageSize.value,
      page: currentPage.value,
    })

    recyclingList.value = response.data.content
    recyclingTotal.value = response.data.totalElements
  }
  catch (error) {
    console.error('获取待回收数据失败:', error)
    ElMessage.error('获取待回收数据失败')
    recyclingList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取所有数据
 */
async function fetchAllData() {
  await Promise.all([
    fetchInspectionData(),
    fetchInstallationData(),
    fetchRecyclingData(),
  ])
}

/**
 * 处理服务项点击
 * @param item - 服务项
 */
function handleServiceClick(item: FlowItem) {
  // 处理服务项点击事件
  console.log('点击服务项:', item)
}

/**
 * 处理更多点击 - 待勘查
 */
function handleMoreInspectionClick() {
  // 处理更多点击
  console.log('点击更多 - 待勘查')
}

/**
 * 处理更多点击 - 待安装
 */
function handleMoreInstallationClick() {
  // 处理更多点击
  console.log('点击更多 - 待安装')
}

/**
 * 处理更多点击 - 待回收
 */
function handleMoreRecyclingClick() {
  // 处理更多点击
  console.log('点击更多 - 待回收')
}

/**
 * 格式化流程类型显示
 */
function getFlowTypeLabel(flow: number): string {
  const flowLabels: Record<number, string> = {
    0: '工程创建',
    1: '安装预约',
    2: '现场勘察',
    3: '上门安装',
    4: '接入监管',
    5: '施工完成',
    6: '回收预约',
    7: '上门回收',
    8: '监管结束',
  }
  return flowLabels[flow] || '未知'
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAllData()
})
</script>

<template>
  <div class="grid grid-cols-3 gap-4">
    <!-- 待勘查 -->
    <Card title="待勘查">
      <template #header>
        <button
          class="text-sm text-primary hover:text-primary/80"
          @click="handleMoreInspectionClick"
        >
          更多
        </button>
      </template>

      <div v-loading="loading" class="mt-4 space-y-3">
        <div
          v-for="item in inspectionList"
          :key="item.id"
          class="cursor-pointer border border-border rounded-md p-3 transition-colors hover:bg-muted/50"
          @click="handleServiceClick(item)"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="text-sm font-medium">
                {{ getFlowTypeLabel(item.flow) }}
              </div>
              <div class="mt-1 text-xs text-muted-foreground">
                项目ID: {{ item.projectId }}
              </div>
              <div class="mt-1 text-xs text-muted-foreground">
                订单ID: {{ item.orderId }}
              </div>
            </div>
            <div class="text-right">
              <div class="text-xs text-muted-foreground">
                {{ FormatDate(item.createdAt, 'MM-DD HH:mm') }}
              </div>
            </div>
          </div>
        </div>

        <div v-if="inspectionList.length === 0 && !loading" class="py-8 text-center text-muted-foreground">
          暂无数据
        </div>
      </div>
    </Card>

    <!-- 待安装 -->
    <Card title="待安装">
      <template #header>
        <button
          class="text-sm text-primary hover:text-primary/80"
          @click="handleMoreInstallationClick"
        >
          更多
        </button>
      </template>

      <div v-loading="loading" class="mt-4 space-y-3">
        <div
          v-for="item in installationList"
          :key="item.id"
          class="cursor-pointer border border-border rounded-md p-3 transition-colors hover:bg-muted/50"
          @click="handleServiceClick(item)"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="text-sm font-medium">
                {{ getFlowTypeLabel(item.flow) }}
              </div>
              <div class="mt-1 text-xs text-muted-foreground">
                项目ID: {{ item.projectId }}
              </div>
              <div class="mt-1 text-xs text-muted-foreground">
                订单ID: {{ item.orderId }}
              </div>
            </div>
            <div class="text-right">
              <div class="text-xs text-muted-foreground">
                {{ FormatDate(item.createdAt, 'MM-DD HH:mm') }}
              </div>
            </div>
          </div>
        </div>

        <div v-if="installationList.length === 0 && !loading" class="py-8 text-center text-muted-foreground">
          暂无数据
        </div>
      </div>
    </Card>

    <!-- 待回收 -->
    <Card title="待回收">
      <template #header>
        <button
          class="text-sm text-primary hover:text-primary/80"
          @click="handleMoreRecyclingClick"
        >
          更多
        </button>
      </template>

      <div v-loading="loading" class="mt-4 space-y-3">
        <div
          v-for="item in recyclingList"
          :key="item.id"
          class="cursor-pointer border border-border rounded-md p-3 transition-colors hover:bg-muted/50"
          @click="handleServiceClick(item)"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="text-sm font-medium">
                {{ getFlowTypeLabel(item.flow) }}
              </div>
              <div class="mt-1 text-xs text-muted-foreground">
                项目ID: {{ item.projectId }}
              </div>
              <div class="mt-1 text-xs text-muted-foreground">
                订单ID: {{ item.orderId }}
              </div>
            </div>
            <div class="text-right">
              <div class="text-xs text-muted-foreground">
                {{ FormatDate(item.createdAt, 'MM-DD HH:mm') }}
              </div>
            </div>
          </div>
        </div>

        <div v-if="recyclingList.length === 0 && !loading" class="py-8 text-center text-muted-foreground">
          暂无数据
        </div>
      </div>
    </Card>
  </div>
</template>
