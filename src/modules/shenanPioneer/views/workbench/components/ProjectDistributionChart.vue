<!--
  工程区域分布组件
  使用 AntV G2 显示工程区域分布柱状图
-->
<script setup lang="ts">
import type { ProjectRegionItem, ProjectRegionParams } from '@shenanPioneer/api'
import { Chart } from '@antv/g2'
import { fetchProjectRegionStatistics } from '@shenanPioneer/api'
import RegionCascader from '@shenanPioneer/components/RegionCascader/RegionCascader.vue'
import { useChart } from '@/modules/shenanPioneer/hooks/useChart'
import { useRegionAuthStore } from '@/modules/shenanPioneer/store/region'
import Card from './Card.vue'

// ==================== 类型定义 ====================
interface ChartDataItem {
  /** 区域名称 */
  region: string
  /** 工程数量 */
  count: number
}
let chart: Chart | null = null

// ==================== Store ====================
const regionStore = useRegionAuthStore({ root: false })
const { chartContainer, theme, setChart, updateChart } = useChart(chart)

// ==================== 响应式数据 ====================
const loading = ref<boolean>(false)
const data = ref<ChartDataItem[]>([])
const region = ref<string[]>([])

// API 请求参数
const requestParams = reactive<ProjectRegionParams>({
  regionId: '',
  regionPid: '',
  regionCid: '',
})
// ==================== 方法定义 ====================
/**
 * 获取工程区域分布数据
 */
async function fetchProjectRegionData() {
  try {
    loading.value = true
    const response = await fetchProjectRegionStatistics(requestParams)

    // 将 API 数据转换为图表所需格式
    data.value = response.data.map((item: ProjectRegionItem) => ({
      region: item.title,
      count: item.projectCount,
    }))

    // 更新图表数据
    updateChart(data.value)
  }
  catch (error) {
    console.error('获取工程区域分布数据失败:', error)
    // 发生错误时使用空数据
    data.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 获取用户区域树
 */
async function getUserRegionTree() {
  try {
    await regionStore.query()
    if (regionStore.regionTree.value?.[0]?.id) {
      region.value = [regionStore.regionTree.value[0].id]
    }
  }
  catch (error) {
    console.error('获取用户区域树失败:', error)
  }
}

/**
 * 初始化图表
 */
function initChart() {
  if (!chartContainer.value) {
    return
  }

  chart = new Chart({
    container: chartContainer.value,
    autoFit: true,
    height: 350,
    theme: theme.value,
  })

  chart
    .interval()
    .data(data.value)
    .encode('x', 'region')
    .encode('y', 'count')
    .axis('x', { title: '区域' })
    .axis('y', { title: '工程数量' })
    .encode('color', 'region')
    .style('radius', 4)
    .tooltip({
      title: '区域分布',
      items: [
        {
          name: '工程数量',
          field: 'count',
          valueFormatter: (value: number) => `${value} 个`,
        },
      ],
    })
    .interaction('elementHighlight', true)

  chart.render()

  setChart(chart)
}

// ==================== 生命周期 ====================
onMounted(async () => {
  await getUserRegionTree()

  initChart()
})

// ==================== 监听器 ====================

watch(region, (newVal) => {
  const [regionPid, regionId, regionCid] = newVal
  if (regionPid) {
    requestParams.regionPid = regionPid
  }
  if (regionId) {
    requestParams.regionId = regionId
  }
  if (regionCid) {
    requestParams.regionCid = regionCid
  }
  fetchProjectRegionData()
})
</script>

<template>
  <Card title="工程区域分布">
    <template #header>
      <div>
        <RegionCascader v-model="region" is-auth-region placeholder="请选择区域" :props="{ checkStrictly: true }" />
      </div>
    </template>
    <div v-loading="loading" class="">
      <div ref="chartContainer" class="min-h-80 w-full" />
    </div>
  </Card>
</template>
