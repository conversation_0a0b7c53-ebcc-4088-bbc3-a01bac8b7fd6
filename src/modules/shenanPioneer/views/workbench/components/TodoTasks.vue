<!--
  待办事项组件
  显示用户的待办任务列表，支持标签页切换
-->
<script setup lang="ts">
import type { MessageRemindItem } from '@shenanPioneer/api'
import type { TagProps } from 'element-plus'
import { fetchDoneMessages, fetchTodoMessages } from '@shenanPioneer/api'
import { FormatDate } from '@shencom/utils'
import { ElMessage } from 'element-plus'
import { MessageResolveFlag } from '@/modules/shenanPioneer/enum/message'
import { getDateRange } from '@/modules/shenanPioneer/utils/date'
import Card from './Card.vue'

/**
 * 待办事项接口
 */
interface TodoItem {
  /** 唯一标识 */
  id: string
  /** 日期 */
  date: string
  /** 标题 */
  title: string
  /** 优先级 */
  priority: string
  /** 原始数据 */
  raw?: MessageRemindItem
}

const radioOptions = ref([
  { label: '我的待办', value: MessageResolveFlag.待办 },
  { label: '我的已办', value: MessageResolveFlag.本人已办 },
])

const filterOptions = ref(['今日', '昨日', '全部'])

// ==================== 响应式数据 ====================
const loading = ref(false)
const activeTab = ref<MessageResolveFlag>(MessageResolveFlag.待办)
const selectedFilters = ref<string>('今日')
const todoList = ref<TodoItem[]>([])
const totalCount = ref(0)

// ==================== 计算属性 ====================
const filteredTodoList = computed(() => {
  // 限制显示5条
  return todoList.value.slice(0, 5)
})

// ==================== 方法定义 ====================
/**
 * 转换消息数据为显示格式
 * @param items - 消息列表
 * @returns 转换后的数据
 */
function transformTodoData(items: MessageRemindItem[]): TodoItem[] {
  return items.map((item) => {
    // 提取工程名称
    const titleMatch = item.content.match(/工程【(.+?)】/)
    const projectName = titleMatch ? titleMatch[1] : '未知工程'

    // 格式化日期
    const date = FormatDate(item.createdAt, 'MM-DD')

    // 根据类型确定优先级
    let priority = '中'
    if (item.type === 1 || item.type === 2) {
      priority = '高'
    }
    else if (item.type === 3 || item.type === 4) {
      priority = '中'
    }
    else if (item.type === 5) {
      priority = '低'
    }

    return {
      id: item.id,
      date,
      title: `工程【${projectName}】`,
      priority,
      raw: item,
    }
  })
}

/**
 * 获取待办数据
 */
async function fetchTodos(): Promise<void> {
  loading.value = true
  try {
    const dateRange = getDateRange(selectedFilters.value)
    let response

    if (activeTab.value === MessageResolveFlag.待办) {
      // 我的待办
      response = await fetchTodoMessages(dateRange)
    }
    else {
      // 我的已办
      response = await fetchDoneMessages(dateRange)
    }

    todoList.value = transformTodoData(response.content)
    totalCount.value = response.totalElements
  }
  catch (error) {
    console.error('获取待办数据失败:', error)
    ElMessage.error('获取待办数据失败')
    todoList.value = []
  }
  finally {
    loading.value = false
  }
}

/**
 * 处理待办项点击
 * @param item - 待办项
 */
function handleTodoClick(item: TodoItem): void {
  // 处理待办项点击
  console.log('点击待办项:', item)
}

/**
 * 处理更多点击
 */
function handleMoreClick(): void {
  // 处理更多点击
  console.log('点击更多')
}

/**
 * 处理标签页变化
 */
function handleTabChange(): void {
  // 重新获取数据
  fetchTodos()
}

/**
 * 获取优先级类型
 * @param priority - 优先级
 * @returns 类型
 */
function getPriorityType(priority: string): TagProps['type'] {
  switch (priority) {
    case '高':
      return 'danger'
    case '中':
      return 'warning'
    case '低':
      return 'info'
    default:
      return 'primary'
  }
}

// ==================== 生命周期 ====================
onMounted(() => {
  fetchTodos()
})

// ==================== 监听器 ====================
watch(activeTab, () => {
  handleTabChange()
})

watch(selectedFilters, () => {
  // 时间过滤变化时重新获取数据
  fetchTodos()
})
</script>

<template>
  <Card title="待办事项">
    <template #header>
      <el-radio-group v-model="selectedFilters" size="small" :disabled="loading">
        <el-radio-button v-for="filter in filterOptions" :key="filter" :label="filter">
          {{ filter }}
        </el-radio-button>
      </el-radio-group>
    </template>
    <div class="py-2">
      <el-radio-group v-model="activeTab" :disabled="loading">
        <el-radio-button
          v-for="item in radioOptions" :key="item.value" v-model="activeTab" :label="item.value"
          size="small"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
    </div>
    <div v-loading="loading" class="flex-1">
      <div class="h-full flex flex-col">
        <div class="flex-1">
          <div
            v-if="filteredTodoList.length === 0 && !loading"
            class="flex items-center justify-center py-8 text-gray-500"
          >
            暂无数据
          </div>
          <div
            v-for="item in filteredTodoList" :key="item.id"
            class="flex cursor-pointer items-center py-3 hover:text-primary" @click="handleTodoClick(item)"
          >
            <span class="mr-2 text-sm text-foreground/60">{{ item.date }}</span>
            <span class="flex-1 text-sm leading-relaxed ellipsis-1">{{ item.title }}</span>
            <ElTag v-if="item.priority" :type="getPriorityType(item.priority)" size="small" class="ml-2">
              {{ item.priority }}
            </ElTag>
          </div>
        </div>

        <div
          v-if="totalCount > 5"
          class="flex cursor-pointer items-center justify-center rounded p-2 text-sm text-primary transition-colors duration-300 hover:bg-primary/5"
          @click="handleMoreClick"
        >
          <span class="mr-1">更多</span>
          <FaIcon name="i-ep:arrow-right" />
        </div>
      </div>
    </div>
  </Card>
</template>
