<!--
  实时警告组件
  显示实时警告列表
-->
<script setup lang="ts">
import type { WarningEventItem, WarningEventParams } from '@shenanPioneer/api'
import { fetchWarningEventList } from '@shenanPioneer/api'
import { ElMessage } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import Card from './Card.vue'

// 路由实例 (暂时未使用，保留用于后续路由跳转功能)
const _router = useRouter()

// 响应式数据
const loading = ref(false)
const warningEvents = ref<WarningEventItem[]>([])
const total = ref(0)
const currentPage = ref(0)
const pageSize = ref(5) // 显示5条数据

// 请求参数
const requestParams = ref<WarningEventParams>({
  size: pageSize.value,
  page: currentPage.value,
})

// 获取实时警告数据
async function fetchData() {
  loading.value = true
  try {
    const response = await fetchWarningEventList(requestParams.value)
    warningEvents.value = response.data?.content || []
    total.value = response.data?.totalElements || 0
  }
  catch (error) {
    console.error('获取实时警告数据失败:', error)
    ElMessage.error('获取实时警告数据失败')
    warningEvents.value = []
  }
  finally {
    loading.value = false
  }
}

// 格式化时间
function formatTime(timestamp: number): string {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  // 小于1分钟显示"刚刚"
  if (diff < 60000) {
    return '刚刚'
  }

  // 小于1小时显示"X分钟前"
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000)
    return `${minutes}分钟前`
  }

  // 小于24小时显示"X小时前"
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000)
    return `${hours}小时前`
  }

  // 超过24小时显示具体时间
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 处理图片加载错误
function handleImageError(event: Event) {
  const target = event.target as HTMLImageElement
  if (target) {
    target.style.display = 'none'
  }
}

// 处理更多按钮点击
function handleMoreClick() {
  // 跳转到实时警告列表页面
  // 这里可以根据实际的路由配置进行跳转
  // router.push('/warning-events')

  // 暂时使用 console.warn 提示，避免 eslint 错误
  console.warn('跳转到实时警告详情页面功能待实现，需要配置对应的路由')
}

// 处理警告项点击
function handleWarningClick(item: WarningEventItem) {
  // 跳转到警告详情页面
  // router.push(`/warning-events/${item.id}`)

  // 暂时使用 console.warn 提示
  console.warn('跳转到警告详情页面功能待实现', item)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<template>
  <Card title="实时警告">
    <template #header>
      <div
        class="hover:bg-primary-50 flex cursor-pointer items-center justify-center rounded p-2 text-sm text-primary transition-colors duration-300"
        @click="handleMoreClick"
      >
        <span class="mr-1">更多</span>
        <FaIcon name="i-ep:arrow-right" />
      </div>
    </template>

    <div class="mt-2 h-[350px] flex flex-col">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex flex-1 items-center justify-center">
        <div class="flex flex-col items-center gap-3 text-muted-foreground">
          <div class="relative">
            <div class="h-8 w-8 animate-spin border-3 border-primary border-t-transparent rounded-full" />
            <div class="absolute inset-0 h-8 w-8 animate-pulse border-3 border-primary/50 rounded-full" />
          </div>
          <span class="animate-pulse text-sm">加载中...</span>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!warningEvents.length" class="flex flex-1 items-center justify-center">
        <div class="text-center text-gray-400">
          <div class="mb-4 flex justify-center">
            <div class="h-16 w-16 flex items-center justify-center rounded-full bg-gray-100">
              <i class="text-2xl">🔔</i>
            </div>
          </div>
          <p class="text-sm font-medium">
            暂无实时警告
          </p>
          <p class="mt-1 text-xs text-gray-400">
            系统运行正常
          </p>
        </div>
      </div>

      <!-- 警告列表 -->
      <div v-else class="flex-1 overflow-hidden">
        <div class="h-full overflow-y-auto space-y-3">
          <div
            v-for="item in warningEvents" :key="item.id" dark=" bg-background"
            hover="border-red-200 bg-red/10 shadow-sm"
            class="group flex cursor-pointer gap-3 border border-transparent rounded-lg bg-primary-foreground p-3 transition-all duration-200"
            @click="handleWarningClick(item)"
          >
            <!-- 警告图片 -->
            <div class="flex-shrink-0">
              <div class="relative">
                <img
                  v-if="item.pics" :src="item.pics" :alt="item.typeName"
                  class="h-12 w-12 border-2 border-gray-200 rounded-lg object-cover transition-all duration-200 group-hover:border-red-300"
                  @error="handleImageError"
                >
                <div
                  v-else
                  class="h-12 w-12 flex items-center justify-center border-2 border-red-200 rounded-lg bg-red-100 transition-all duration-200 group-hover:border-red-300 group-hover:bg-red-200"
                >
                  <i class="text-lg text-red">⚠️</i>
                </div>
                <!-- 警告状态指示器 -->
                <div class="absolute h-3 w-3 animate-pulse rounded-full bg-red ring-2 ring-white -right-1 -top-1" />
              </div>
            </div>

            <!-- 警告信息 -->
            <div class="min-w-0 flex-1">
              <div class="flex items-start justify-between gap-2">
                <h4 class="truncate text-sm text-foreground font-medium transition-colors">
                  {{ item.projectName }}
                </h4>
                <span class="flex-shrink-0 text-xs text-muted-foreground transition-colors group-hover:text-foreground">
                  {{ formatTime(item.eventAt) }}
                </span>
              </div>

              <div class="mt-1 flex items-center gap-1">
                <div class="h-1.5 w-1.5 rounded-full bg-red" />
                <p class="text-xs text-red font-medium">
                  {{ item.typeName }}
                </p>
              </div>
              <div class="mt-1 flex items-center gap-1 text-xs text-secondary-foreground">
                <span class="text-xs">📍</span>
                <p class="truncate">
                  {{ item.districtName }}{{ item.streetName ? ` · ${item.streetName}` : '' }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Card>
</template>
