import type { Route } from '#/global'
import type { RouteRecordRaw } from 'vue-router'
import { OldPlatformService } from '@admin/service'
import generatedRoutes from 'virtual:generated-pages'
import { setupLayouts } from 'virtual:meta-layouts'
import { $t } from '@/locales'
import pinia from '@/store'
import login from '../modules/login/main'

// 固定路由（默认路由）
const constantRoutes: RouteRecordRaw[] = [
  ...login.routes,
  // {
  //   path: '/login',
  //   name: 'login',
  //   component: () => import('@/views/login.vue'),
  //   meta: {
  //     whiteList: true,
  //     title: $t('app.route.login'),
  //   },
  // },
  {
    path: '/:all(.*)*',
    name: 'notFound',
    component: () => import('@/views/[...all].vue'),
    meta: {
      title: '找不到页面',
    },
  },
]

// 系统路由
const systemRoutes: RouteRecordRaw[] = [
  {
    path: '/',
    component: () => import('@/layouts/index.vue'),
    meta: {
      breadcrumb: false,
    },
    children: [
      {
        path: '',
        component: () => import('@/views/index.vue'),
        meta: {
          title: $t(useSettingsStore(pinia).settings.home.title),
          icon: 'i-ant-design:home-twotone',
          breadcrumb: false,
          cache: true,
        },
      },
      {
        path: `${OldPlatformService.routerName}/:id(\\d+)?/:url+`,
        name: OldPlatformService.routerName,
        redirect: '',
        meta: {
          admin: true,
          cache: true,
          icon: 'i-ant-design:home-twotone',
        },
      },
      {
        path: 'reload',
        name: 'reload',
        component: () => import('@/views/reload.vue'),
        meta: {
          title: $t('app.route.reload'),
          breadcrumb: false,
        },
      },
      {
        meta: {
          icon: 'mdi:lightbulb-error-outline',
          title: '缺省页',
          cache: true,
        },
        name: 'Fallback',
        path: '/fallback',
        children: [
          {
            name: 'Fallback403',
            path: '403',
            component: () => import('@/views/fallback/forbidden.vue'),
            meta: {
              icon: 'mdi:do-not-disturb-alt',
              title: '403',
              cache: true,
            },
          },
          {
            name: 'Fallback404',
            path: '404',
            component: () => import('@/views/fallback/not-found.vue'),
            meta: {
              icon: 'mdi:table-off',
              title: '404',
              cache: true,
            },
          },
          {
            name: 'Fallback500',
            path: '500',
            component: () =>
              import('@/views/fallback/internal-error.vue'),
            meta: {
              icon: 'mdi:server-network-off',
              title: '500',
              cache: true,
            },
          },
          {
            name: 'FallbackOffline',
            path: 'offline',
            component: () => import('@/views/fallback/offline.vue'),
            meta: {
              icon: 'mdi:offline',
              title: $t('route.offline'),
              cache: true,
            },
          },
          {
            name: 'FallbackComingSoon',
            path: 'coming-soon',
            component: () => import('@/views/fallback/coming-soon.vue'),
            meta: {
              icon: 'mdi:lightbulb-error-outline',
              title: '即将推出',
              cache: true,
            },
          },
        ],
      },
      // CSS变量展示页面
      {
        path: '/color',
        name: 'colorVariables',
        meta: {
          title: 'CSS变量展示',
          icon: 'i-mdi:palette',
        },
        children: [
          {
            path: '',
            name: 'colorVariablesDisplay',
            component: () => import('@/views/color.vue'),
            meta: {
              title: 'CSS变量展示',
              icon: 'i-mdi:palette',
            },
          },
        ],
      },
    ],
  },
]

// 动态路由（异步路由、导航栏路由）
const asyncRoutes: Route.recordMainRaw[] = [

]

const constantRoutesByFilesystem = generatedRoutes.filter((item) => {
  return item.meta?.enabled !== false && item.meta?.constant === true
})

const asyncRoutesByFilesystem = setupLayouts(generatedRoutes.filter((item) => {
  return item.meta?.enabled !== false && item.meta?.constant !== true && item.meta?.layout !== false
}))

export {
  asyncRoutes,
  asyncRoutesByFilesystem,
  constantRoutes,
  constantRoutesByFilesystem,
  systemRoutes,
}
