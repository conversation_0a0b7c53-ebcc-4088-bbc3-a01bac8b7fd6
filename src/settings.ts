import type { RecursiveRequired, Settings } from '#/global'
import { cloneDeep } from 'es-toolkit'
import settingsDefault from '@/settings.default'
import { merge } from '@/utils/object'

const globalSettings: Settings.all = {
  app: {
    colorScheme: 'light',
    lightTheme: 'blue',
    darkTheme: 'blue',
    enablePermission: true,
    enableDynamicTitle: true,
    enableErrorLog: true,
    routeBaseOn: 'backend',
    enableWatermark: true,
  },
  menu: {
    mode: 'head',
    style: 'line',
    enableSubMenuCollapseButton: true,
    enableHotkeys: true,
  },
  layout: {
    enableMobileAdaptation: true,
  },
  mainPage: {
    transitionMode: 'slide-right',
    iframeCacheMax: 9,
  },
  topbar: {
    mode: 'fixed',
  },
  tabbar: {
    style: 'fashion',
    enableIcon: true,
    dblclickAction: 'reload',
    enableMemory: true,
    enable: true,
    enableHotkeys: true,
  },
  toolbar: {
    breadcrumb: true,
    fullscreen: true,
    pageReload: true,
    colorScheme: true,
    favorites: true,
  },
  breadcrumb: {
    style: 'modern',
    enableMainMenu: true,
  },
  home: {
    fullPath: '/',
  },

  userPreferences: {
    enable: true,
  },
  copyright: {
    enable: false,
    dates: `2020-${new Date().getFullYear()}`,
    company: '', // 公司名称
    website: '', // 公司网站
    beian: '', // 备案号
  },
}

export default merge(globalSettings, cloneDeep(settingsDefault)) as RecursiveRequired<Settings.all>
