<route lang="yaml">
meta:
  title: CSS变量展示
  icon: i-mdi:palette
</route>

<script setup lang="ts">
import { TinyColor } from '@ctrl/tinycolor'
import { useClipboard } from '@vueuse/core'
import { ElMessage } from 'element-plus'
import ColorScheme from '@/layouts/components/Topbar/Toolbar/ColorScheme/index.vue'
import themes from '../../themes'

defineOptions({
  name: 'CssVariablesDisplay',
})

const settingsStore = useSettingsStore()
const { copy } = useClipboard()

// 搜索关键词
const searchKeyword = ref('')

// 搜索选项
const searchOptions = ref({
  includeValues: false, // 是否搜索颜色值
  caseSensitive: false, // 是否区分大小写
})

// 获取所有主题列表
const themeList = computed(() => {
  return Object.keys(themes).map((key) => {
    return {
      label: key as keyof typeof themes,
      value: key,
      lightColors: (themes as any)[key].light || {},
      darkColors: (themes as any)[key].dark || {},
    }
  })
})

const currentTheme = computed(() => settingsStore.currentColorScheme === 'dark'
  ? settingsStore.settings.app.darkTheme
  : settingsStore.settings.app.lightTheme)

// 当前主题的颜色变量
const currentThemeColors = computed(() => {
  const themeData = (themes as any)[currentTheme.value]
  const themeColors = themeData?.[settingsStore.currentColorScheme!] || {}
  return Object.keys(themeColors)
})

// 颜色分组
const stylesheets = ref<string[]>([])
const allVarKeys = computed(() => [...new Set([...stylesheets.value, ...currentThemeColors.value])])

const allVarKeysObj = computed(() => {
  return filterCssColorVars(allVarKeys.value)
})

// 过滤后的颜色变量
const filteredColors = computed(() => {
  const colors = allVarKeysObj.value
  if (!searchKeyword.value) {
    return colors
  }

  const keyword = searchOptions.value.caseSensitive
    ? searchKeyword.value
    : searchKeyword.value.toLowerCase()

  return Object.fromEntries(
    Object.entries(colors).filter(([key, value]) => {
      const searchKey = searchOptions.value.caseSensitive ? key : key.toLowerCase()
      const searchValue = searchOptions.value.caseSensitive ? value as string : (value as string).toLowerCase()

      const keyMatch = searchKey.includes(keyword)
      const valueMatch = searchOptions.value.includeValues && searchValue.includes(keyword)

      return keyMatch || valueMatch
    }),
  )
})

// 颜色变量分类
const colorCategories = computed(() => {
  const colors = filteredColors.value

  const categories: Record<string, Array<{ key: string, value: string }>> = {
    customClass: [],
    shadcn: [],
    layout: [],
    element: [],
  }

  Object.entries(colors).forEach(([key, value]) => {
    const colorItem = { key, value }
    if (key.startsWith('--el-')) {
      categories.element.push(colorItem)
    }
    // 布局相关变量（--g- 开头）
    else if (key.startsWith('--g-')) {
      categories.layout.push(colorItem)
    }

    // Shadcn/UI 变量
    else if ([
      '--background',
      '--foreground',
      '--card',
      '--card-foreground',
      '--popover',
      '--popover-foreground',
      '--primary',
      '--primary-foreground',
      '--secondary',
      '--secondary-foreground',
      '--muted',
      '--muted-foreground',
      '--accent',
      '--accent-foreground',
      '--destructive',
      '--destructive-foreground',
      '--border',
      '--input',
      '--ring',
    ].some(prefix => key.startsWith(prefix))) {
      categories.shadcn.push(colorItem)
    }
  })

  return categories
})

// 强制刷新变量的触发器
const refreshTrigger = ref(0)

// 监听主题变化，刷新变量
watch([
  () => settingsStore.currentColorScheme,
  () => settingsStore.settings.app.lightTheme,
  () => settingsStore.settings.app.darkTheme,
], () => {
  // 延迟一点时间让DOM更新完成
  nextTick(() => {
    refreshTrigger.value++
  })
}, { immediate: true })

onMounted(() => {
  stylesheets.value = extractCssVarsFromStylesheets()
})

// 获取css变量值
function getCssVarValue(key: string) {
  return getComputedStyle(document.body).getPropertyValue(key)
}

function isColorValue(value: string) {
  if (!value) {
    return null
  }

  const tiny = new TinyColor(value)
  if (tiny.isValid) {
    return tiny
  }

  // HSL数值格式
  const HSLValue = /^\d+(?:\.\d+)?\s+\d+(?:\.\d+)?%\s+\d+(?:\.\d+)?%$/

  if (HSLValue.test(value.trim())) {
    return new TinyColor(`hsl(${value})`)
  }

  const colorValuePatterns = [
    /^#[0-9a-f]{3,8}$/i, // 十六进制颜色
    /^rgb\(/, // RGB颜色
    /^rgba\(/, // RGBA颜色
    /^hsl\(/, // HSL颜色
    /^hsla\(/, // HSLA颜色
    /^var\(--/, // CSS变量引用
  ]

  const flag = colorValuePatterns.some(pattern => pattern.test(value.trim()))

  if (flag) {
    return tiny
  }

  return null
}

function extractCssVarsFromStylesheets() {
  const vars = new Set()

  Array.from(document.styleSheets).forEach((sheet) => {
    try {
      Array.from(sheet.cssRules).forEach((rule) => {
        if (rule instanceof CSSStyleRule) {
          const matches = rule.style.cssText.match(/--[\w-]+/g)
          if (matches) {
            matches.forEach(v => vars.add(v))
          }
        }
      })
    }
    catch (e) { /* 忽略跨域错误 */ }
  })

  return [...vars] as string[]
}

// 过滤css变量
function filterCssColorVars(vars: string[]) {
  const obj = vars.reduce((acc, key) => {
    const val = getCssVarValue(key)
    const tiny = isColorValue(val)
    if (tiny) {
      acc[key] = tiny.toHexString()
    }
    return acc
  }, {} as Record<string, string>)

  return obj
}

// 默认复制文本类名（点击卡片时）
async function copyClassName(variableName: string) {
  try {
    await copy(variableName)
    ElMessage.success(`已复制: ${variableName}`)
  }
  catch {
    ElMessage.error('复制失败')
  }
}

// 调试：打印所有变量
function debugVariables() {
  const allVars = Object.keys(allVarKeysObj.value)

  console.log('变量', allVars)
  ElMessage.success(`已打印 ${allVars.length} 个变量到控制台`)
}

// 切换主题
function switchTheme(themeName: string) {
  if (settingsStore.currentColorScheme === 'dark') {
    settingsStore.settings.app.darkTheme = themeName as any
  }
  else {
    settingsStore.settings.app.lightTheme = themeName as any
  }
}

function replaceDash(str: string) {
  return str.replace(/^--/, '')
}

const categoryOptions = ref([
  { label: 'Shadcn/UI 颜色', value: 'shadcn', icon: 'i-mdi:dots-grid' },
  { label: '布局相关颜色', value: 'layout', icon: 'i-mdi:view-dashboard' },
  { label: 'Element Plus 颜色', value: 'element', icon: 'i-mdi:widgets' },
])
</script>

<template>
  <div class="min-h-screen bg-background">
    <!-- 顶部导航栏 -->
    <div class="sticky top-0 z-10 border-b border-border bg-background/80 backdrop-blur-md">
      <div class="mx-auto mb-4 px-4 lg:px-8 sm:px-6">
        <div class="h-18 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary/10">
              <i class="i-mdi:palette text-xl text-primary" />
            </div>
            <div>
              <h1 class="text-xl text-foreground font-semibold">
                CSS变量颜色展示
              </h1>
              <p class="text-sm text-muted-foreground">
                项目中所有CSS变量的可视化展示
              </p>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <ColorScheme />

            <el-button type="primary" @click="debugVariables">
              <i class="i-mdi:bug" />
              调试
            </el-button>

            <div class="w-40">
              <el-select :model-value="currentTheme" class="" @update:model-value="switchTheme">
                <el-option v-for="theme in themeList" :key="theme.value" :label="theme.label" :value="theme.value" />
              </el-select>
            </div>
          </div>
        </div>

        <div class="mt-2 flex items-center justify-between">
          <div class="flex items-center">
            <span class="text-sm text-muted-foreground">总变量数：</span>
            <span class="text-sm text-foreground font-medium">{{ Object.keys(allVarKeysObj).length }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-muted-foreground">显示变量：</span>
            <span class="text-sm text-foreground font-medium">{{ Object.keys(filteredColors).length }}</span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-muted-foreground">当前模式：</span>
            <span class="text-sm text-foreground font-medium">
              {{ settingsStore.currentColorScheme === 'dark' ? '暗色模式' : '亮色模式' }}
            </span>
          </div>
          <div class="flex items-center">
            <span class="text-sm text-muted-foreground">当前主题：</span>
            <span class="text-sm text-primary font-medium">
              {{ currentTheme }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="mx-auto border-b border-border px-4 pb-4 lg:px-8 sm:px-6">
      <div class="h-18 flex items-center space-x-4">
        <div class="h-10 w-10 flex items-center justify-center rounded-lg bg-primary/10">
          <i class="i-mdi:filter-variant text-xl text-primary" />
        </div>
        <div>
          <h1 class="text-xl text-foreground font-semibold">
            搜索与过滤
          </h1>
        </div>
      </div>
      <div class="flex items-center justify-between rounded-xl bg-card">
        <div>
          <el-input v-model="searchKeyword" placeholder="输入变量名或颜色值..." clearable>
            <template #prefix>
              <i class="i-mdi:magnify text-muted-foreground" />
            </template>
          </el-input>
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-foreground">搜索颜色值：</span>
          <el-switch v-model="searchOptions.includeValues" />
        </div>
        <div class="flex items-center justify-between">
          <span class="text-sm text-foreground">区分大小写：</span>
          <el-switch v-model="searchOptions.caseSensitive" />
        </div>
      </div>
    </div>

    <!-- 颜色变量展示区域 -->
    <div class="mx-auto px-4 py-4 lg:px-8 sm:px-6">
      <div
        v-for="(items, category) in colorCategories" v-show="items.length > 0" :key="category"
        class="mb-4 space-y-6"
      >
        <div class="flex items-center space-x-3">
          <div class="h-8 w-8 flex items-center justify-center rounded-lg bg-primary/10">
            <i class="text-primary" :class="categoryOptions.find(item => item.value === category)?.icon" />
          </div>
          <div>
            <h2 class="text-xl text-foreground font-semibold">
              {{ categoryOptions.find(item => item.value === category)?.label }}
            </h2>
            <p class="text-sm text-muted-foreground">
              {{ items.length }} 个变量
            </p>
          </div>
        </div>

        <div
          class="grid grid-cols-1 gap-4 rounded-xl bg-foreground/10 p-4 2xl:grid-cols-5 lg:grid-cols-3 sm:grid-cols-2 xl:grid-cols-4"
        >
          <div
            v-for="{ key, value } in items" :key="key"
            class="group relative overflow-hidden border border-border rounded-xl bg-card transition-all duration-200 hover:shadow-lg hover:shadow-primary/5 hover:-translate-y-1"
          >
            <div
              class="h-12 w-full flex items-center justify-end border-b border-border px-2"
              :style="{ backgroundColor: value }" @click="copyClassName(key)"
            >
              <i v-if="category !== 'shadcn'" class="i-mdi:content-copy px-4 py-2 text-muted-foreground" />
            </div>

            <div class="p-2 space-y-2">
              <div class="text-sm text-foreground font-medium font-mono">
                {{ value }}
              </div>
              <div class="rounded-md bg-muted/50 p-2" @click="copyClassName(`var(${key})`)">
                <div class="flex items-center justify-between text-xs font-mono">
                  <span class="text-primary">var({{ key }})</span>
                  <i class="i-mdi:content-copy p-2 text-sm text-muted-foreground" />
                </div>
              </div>
              <template v-if="category === 'shadcn'">
                <div class="rounded-md bg-muted/50 p-2" @click="copyClassName(`text-${replaceDash(key)}`)">
                  <div class="flex items-center justify-between font-mono">
                    <span class="text-xs text-primary">.text-{{ replaceDash(key) }}</span>
                    <i class="i-mdi:content-copy p-2 text-sm text-muted-foreground" />
                  </div>
                </div>
                <div class="rounded-md bg-muted/50 p-2" @click="copyClassName(`bg-${replaceDash(key)}`)">
                  <div class="flex items-center justify-between font-mono">
                    <span class="text-xs text-primary">.bg-{{ replaceDash(key) }}</span>
                    <i class="i-mdi:content-copy p-2 text-sm text-muted-foreground" />
                  </div>
                </div>
              </template>
              <!-- 文本示例 -->
            </div>
            <div class="flex items-center bg-foreground/20 px-3 py-2 space-x-2">
              <span class="text-xs text-foreground">示例:</span>
              <span class="text-sm font-medium" :style="{ color: value }">
                Sample Text
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="Object.keys(filteredColors).length === 0" class="flex flex-col items-center justify-center py-16">
      <div class="mb-6 h-20 w-20 flex items-center justify-center rounded-full bg-muted/50">
        <i class="i-mdi:palette-outline text-4xl text-muted-foreground" />
      </div>
      <h3 class="mb-2 text-lg text-foreground font-medium">
        {{ searchKeyword ? '未找到匹配的颜色变量' : '暂无颜色变量' }}
      </h3>
      <p class="text-sm text-muted-foreground">
        {{ searchKeyword ? '尝试调整搜索条件或清空搜索框' : '当前主题下没有可显示的颜色变量' }}
      </p>
      <el-button v-if="searchKeyword" type="primary" class="mt-4" @click="searchKeyword = ''">
        清空搜索
      </el-button>
    </div>
  </div>
</template>
