{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "lib": ["ESNext", "DOM", "DOM.Iterable"], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "#/*": ["./src/types/*"], "@admin/*": ["./packages/*"], "@shenanPioneer/*": ["./src/modules/shenanPioneer/*"]}, "resolveJsonModule": true, "types": ["vite-plugin-pages/client", "vite-plugin-vue-meta-layouts/client", "@intlify/unplugin-vue-i18n/messages", "vite-plugin-app-loading/client", "element-plus/global"], "allowJs": false, "strict": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "sourceMap": true, "esModuleInterop": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "packages/**/*.ts", "packages/**/*.tsx", "packages/**/*.vue"]}