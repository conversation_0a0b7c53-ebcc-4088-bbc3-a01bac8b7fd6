{"compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "target": "ES2022", "lib": ["ES2023"], "moduleDetection": "force", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "allowImportingTsExtensions": true, "strict": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "noUnusedLocals": true, "noUnusedParameters": true, "noEmit": true, "isolatedModules": true, "skipLibCheck": true}, "include": ["package.json", "vite.config.ts", "vite/**/*.ts"]}